package com.jorchi.mqtt.vo;

public class TaskRulesEquipmentVO {
    private Long deviceNodeId;
    private Integer orderNumber;
    private Long duration;
    private Integer orderType;
    private String deviceName;
    private String command;
    private Integer deviceType;

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public Long getDeviceNodeId() {
        return deviceNodeId;
    }

    public void setDeviceNodeId(Long deviceNodeId) {
        this.deviceNodeId = deviceNodeId;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }
}
