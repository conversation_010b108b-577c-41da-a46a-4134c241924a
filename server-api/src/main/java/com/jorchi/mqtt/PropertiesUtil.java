package com.jorchi.mqtt;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

@Component
@Data
public class PropertiesUtil {

    @Value("${mqtt.host}")
    public String MQTT_HOST;
    @Value("${mqtt.clientId}")
    public String MQTT_CLIENT_ID;
    @Value("${mqtt.username}")
    public String MQTT_USER_NAME;
    @Value("${mqtt.password}")
    public String MQTT_PASSWORD;
    @Value("${mqtt.topic}")
    public String MQTT_TOPIC;
    @Value("${mqtt.timeout}")
    public Integer MQTT_TIMEOUT;
    @Value("${mqtt.keepalive}")
    public Integer MQTT_KEEP_ALIVE;
    @Value("${mqtt.qos}")
    public Integer QOS;

//    /*
//       mqtt配置
//     */
//    static {
//        Properties properties = loadMqttProperties();
//        MQTT_HOST = properties.getProperty("host");
//        //MQTT_CLIENT_ID = properties.getProperty("clientId");
//        MQTT_CLIENT_ID = java.util.UUID.randomUUID().toString();
//        MQTT_USER_NAME = properties.getProperty("username");
//        MQTT_PASSWORD = properties.getProperty("password");
//        MQTT_TOPIC = properties.getProperty("topic");
//        MQTT_TIMEOUT = Integer.valueOf(properties.getProperty("timeout"));
//        MQTT_KEEP_ALIVE = Integer.valueOf(properties.getProperty("keepalive"));
//        QOS = Integer.valueOf(properties.getProperty("qos"));
//    }
//
//    private static Properties loadMqttProperties() {
//        InputStream inputstream = PropertiesUtil.class.getResourceAsStream("/application.yml");
//        Properties properties = new Properties();
//        try {
//            properties.load(inputstream);
//            return properties;
//        } catch (IOException e) {
//            throw new RuntimeException(e);
//        } finally {
//            try {
//                if (inputstream != null) {
//                    inputstream.close();
//                }
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
//    }
}
