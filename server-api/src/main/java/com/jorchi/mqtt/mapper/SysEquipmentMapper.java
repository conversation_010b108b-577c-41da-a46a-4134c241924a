package com.jorchi.mqtt.mapper;

import com.jorchi.mqtt.entity.SysEquipment;
import com.jorchi.mqtt.entity.SysEquipmentExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysEquipmentMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int countByExample(SysEquipmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int deleteByExample(SysEquipmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int insert(SysEquipment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int insertSelective(SysEquipment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    List<SysEquipment> selectByExample(SysEquipmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    SysEquipment selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int updateByExampleSelective(@Param("record") SysEquipment record, @Param("example") SysEquipmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int updateByExample(@Param("record") SysEquipment record, @Param("example") SysEquipmentExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int updateByPrimaryKeySelective(SysEquipment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_equipment
     *
     * @mbggenerated
     */
    int updateByPrimaryKey(SysEquipment record);
}