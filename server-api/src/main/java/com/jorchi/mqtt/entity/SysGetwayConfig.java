package com.jorchi.mqtt.entity;

public class SysGetwayConfig {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.id
     *
     * @mbggenerated
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.device_id
     *
     * @mbggenerated
     */
    private String deviceId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.rain_control_hold_time
     *
     * @mbggenerated
     */
    private Integer rainControlHoldTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.rainfall_threshold
     *
     * @mbggenerated
     */
    private String rainfallThreshold;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.sensor_collect_interval
     *
     * @mbggenerated
     */
    private Integer sensorCollectInterval;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.node_report_interval
     *
     * @mbggenerated
     */
    private Integer nodeReportInterval;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.envtemp_sensor_collect_interval
     *
     * @mbggenerated
     */
    private Integer envtempSensorCollectInterval;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.envtemp_node_report_interval
     *
     * @mbggenerated
     */
    private Integer envtempNodeReportInterval;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.gateway_sensor_report_interval
     *
     * @mbggenerated
     */
    private Integer gatewaySensorReportInterval;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.sensors_change_rate
     *
     * @mbggenerated
     */
    private Integer sensorsChangeRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.sensor_collect_mode
     *
     * @mbggenerated
     */
    private Integer sensorCollectMode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.elec_start_time
     *
     * @mbggenerated
     */
    private String elecStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.elec_stop_time
     *
     * @mbggenerated
     */
    private String elecStopTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.watering_mode_timeout
     *
     * @mbggenerated
     */
    private Integer wateringModeTimeout;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.watering_mode_sensor_collect_interval
     *
     * @mbggenerated
     */
    private Integer wateringModeSensorCollectInterval;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sys_getway_config.lora_wor_preamble_length
     *
     * @mbggenerated
     */
    private Integer loraWorPreambleLength;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_getway_config
     *
     * @mbggenerated
     */
    public SysGetwayConfig(Long id, String deviceId, Integer rainControlHoldTime, String rainfallThreshold, Integer sensorCollectInterval, Integer nodeReportInterval, Integer envtempSensorCollectInterval, Integer envtempNodeReportInterval, Integer gatewaySensorReportInterval, Integer sensorsChangeRate, Integer sensorCollectMode, String elecStartTime, String elecStopTime, Integer wateringModeTimeout, Integer wateringModeSensorCollectInterval, Integer loraWorPreambleLength) {
        this.id = id;
        this.deviceId = deviceId;
        this.rainControlHoldTime = rainControlHoldTime;
        this.rainfallThreshold = rainfallThreshold;
        this.sensorCollectInterval = sensorCollectInterval;
        this.nodeReportInterval = nodeReportInterval;
        this.envtempSensorCollectInterval = envtempSensorCollectInterval;
        this.envtempNodeReportInterval = envtempNodeReportInterval;
        this.gatewaySensorReportInterval = gatewaySensorReportInterval;
        this.sensorsChangeRate = sensorsChangeRate;
        this.sensorCollectMode = sensorCollectMode;
        this.elecStartTime = elecStartTime;
        this.elecStopTime = elecStopTime;
        this.wateringModeTimeout = wateringModeTimeout;
        this.wateringModeSensorCollectInterval = wateringModeSensorCollectInterval;
        this.loraWorPreambleLength = loraWorPreambleLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_getway_config
     *
     * @mbggenerated
     */
    public SysGetwayConfig() {
        super();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.id
     *
     * @return the value of sys_getway_config.id
     *
     * @mbggenerated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.id
     *
     * @param id the value for sys_getway_config.id
     *
     * @mbggenerated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.device_id
     *
     * @return the value of sys_getway_config.device_id
     *
     * @mbggenerated
     */
    public String getDeviceId() {
        return deviceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.device_id
     *
     * @param deviceId the value for sys_getway_config.device_id
     *
     * @mbggenerated
     */
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId == null ? null : deviceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.rain_control_hold_time
     *
     * @return the value of sys_getway_config.rain_control_hold_time
     *
     * @mbggenerated
     */
    public Integer getRainControlHoldTime() {
        return rainControlHoldTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.rain_control_hold_time
     *
     * @param rainControlHoldTime the value for sys_getway_config.rain_control_hold_time
     *
     * @mbggenerated
     */
    public void setRainControlHoldTime(Integer rainControlHoldTime) {
        this.rainControlHoldTime = rainControlHoldTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.rainfall_threshold
     *
     * @return the value of sys_getway_config.rainfall_threshold
     *
     * @mbggenerated
     */
    public String getRainfallThreshold() {
        return rainfallThreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.rainfall_threshold
     *
     * @param rainfallThreshold the value for sys_getway_config.rainfall_threshold
     *
     * @mbggenerated
     */
    public void setRainfallThreshold(String rainfallThreshold) {
        this.rainfallThreshold = rainfallThreshold == null ? null : rainfallThreshold.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.sensor_collect_interval
     *
     * @return the value of sys_getway_config.sensor_collect_interval
     *
     * @mbggenerated
     */
    public Integer getSensorCollectInterval() {
        return sensorCollectInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.sensor_collect_interval
     *
     * @param sensorCollectInterval the value for sys_getway_config.sensor_collect_interval
     *
     * @mbggenerated
     */
    public void setSensorCollectInterval(Integer sensorCollectInterval) {
        this.sensorCollectInterval = sensorCollectInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.node_report_interval
     *
     * @return the value of sys_getway_config.node_report_interval
     *
     * @mbggenerated
     */
    public Integer getNodeReportInterval() {
        return nodeReportInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.node_report_interval
     *
     * @param nodeReportInterval the value for sys_getway_config.node_report_interval
     *
     * @mbggenerated
     */
    public void setNodeReportInterval(Integer nodeReportInterval) {
        this.nodeReportInterval = nodeReportInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.envtemp_sensor_collect_interval
     *
     * @return the value of sys_getway_config.envtemp_sensor_collect_interval
     *
     * @mbggenerated
     */
    public Integer getEnvtempSensorCollectInterval() {
        return envtempSensorCollectInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.envtemp_sensor_collect_interval
     *
     * @param envtempSensorCollectInterval the value for sys_getway_config.envtemp_sensor_collect_interval
     *
     * @mbggenerated
     */
    public void setEnvtempSensorCollectInterval(Integer envtempSensorCollectInterval) {
        this.envtempSensorCollectInterval = envtempSensorCollectInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.envtemp_node_report_interval
     *
     * @return the value of sys_getway_config.envtemp_node_report_interval
     *
     * @mbggenerated
     */
    public Integer getEnvtempNodeReportInterval() {
        return envtempNodeReportInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.envtemp_node_report_interval
     *
     * @param envtempNodeReportInterval the value for sys_getway_config.envtemp_node_report_interval
     *
     * @mbggenerated
     */
    public void setEnvtempNodeReportInterval(Integer envtempNodeReportInterval) {
        this.envtempNodeReportInterval = envtempNodeReportInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.gateway_sensor_report_interval
     *
     * @return the value of sys_getway_config.gateway_sensor_report_interval
     *
     * @mbggenerated
     */
    public Integer getGatewaySensorReportInterval() {
        return gatewaySensorReportInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.gateway_sensor_report_interval
     *
     * @param gatewaySensorReportInterval the value for sys_getway_config.gateway_sensor_report_interval
     *
     * @mbggenerated
     */
    public void setGatewaySensorReportInterval(Integer gatewaySensorReportInterval) {
        this.gatewaySensorReportInterval = gatewaySensorReportInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.sensors_change_rate
     *
     * @return the value of sys_getway_config.sensors_change_rate
     *
     * @mbggenerated
     */
    public Integer getSensorsChangeRate() {
        return sensorsChangeRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.sensors_change_rate
     *
     * @param sensorsChangeRate the value for sys_getway_config.sensors_change_rate
     *
     * @mbggenerated
     */
    public void setSensorsChangeRate(Integer sensorsChangeRate) {
        this.sensorsChangeRate = sensorsChangeRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.sensor_collect_mode
     *
     * @return the value of sys_getway_config.sensor_collect_mode
     *
     * @mbggenerated
     */
    public Integer getSensorCollectMode() {
        return sensorCollectMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.sensor_collect_mode
     *
     * @param sensorCollectMode the value for sys_getway_config.sensor_collect_mode
     *
     * @mbggenerated
     */
    public void setSensorCollectMode(Integer sensorCollectMode) {
        this.sensorCollectMode = sensorCollectMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.elec_start_time
     *
     * @return the value of sys_getway_config.elec_start_time
     *
     * @mbggenerated
     */
    public String getElecStartTime() {
        return elecStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.elec_start_time
     *
     * @param elecStartTime the value for sys_getway_config.elec_start_time
     *
     * @mbggenerated
     */
    public void setElecStartTime(String elecStartTime) {
        this.elecStartTime = elecStartTime == null ? null : elecStartTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.elec_stop_time
     *
     * @return the value of sys_getway_config.elec_stop_time
     *
     * @mbggenerated
     */
    public String getElecStopTime() {
        return elecStopTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.elec_stop_time
     *
     * @param elecStopTime the value for sys_getway_config.elec_stop_time
     *
     * @mbggenerated
     */
    public void setElecStopTime(String elecStopTime) {
        this.elecStopTime = elecStopTime == null ? null : elecStopTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.watering_mode_timeout
     *
     * @return the value of sys_getway_config.watering_mode_timeout
     *
     * @mbggenerated
     */
    public Integer getWateringModeTimeout() {
        return wateringModeTimeout;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.watering_mode_timeout
     *
     * @param wateringModeTimeout the value for sys_getway_config.watering_mode_timeout
     *
     * @mbggenerated
     */
    public void setWateringModeTimeout(Integer wateringModeTimeout) {
        this.wateringModeTimeout = wateringModeTimeout;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.watering_mode_sensor_collect_interval
     *
     * @return the value of sys_getway_config.watering_mode_sensor_collect_interval
     *
     * @mbggenerated
     */
    public Integer getWateringModeSensorCollectInterval() {
        return wateringModeSensorCollectInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.watering_mode_sensor_collect_interval
     *
     * @param wateringModeSensorCollectInterval the value for sys_getway_config.watering_mode_sensor_collect_interval
     *
     * @mbggenerated
     */
    public void setWateringModeSensorCollectInterval(Integer wateringModeSensorCollectInterval) {
        this.wateringModeSensorCollectInterval = wateringModeSensorCollectInterval;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sys_getway_config.lora_wor_preamble_length
     *
     * @return the value of sys_getway_config.lora_wor_preamble_length
     *
     * @mbggenerated
     */
    public Integer getLoraWorPreambleLength() {
        return loraWorPreambleLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sys_getway_config.lora_wor_preamble_length
     *
     * @param loraWorPreambleLength the value for sys_getway_config.lora_wor_preamble_length
     *
     * @mbggenerated
     */
    public void setLoraWorPreambleLength(Integer loraWorPreambleLength) {
        this.loraWorPreambleLength = loraWorPreambleLength;
    }
}