package com.jorchi.mqtt.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SysEarlyRecordExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public SysEarlyRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWarningTypeIsNull() {
            addCriterion("warning_type is null");
            return (Criteria) this;
        }

        public Criteria andWarningTypeIsNotNull() {
            addCriterion("warning_type is not null");
            return (Criteria) this;
        }

        public Criteria andWarningTypeEqualTo(String value) {
            addCriterion("warning_type =", value, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeNotEqualTo(String value) {
            addCriterion("warning_type <>", value, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeGreaterThan(String value) {
            addCriterion("warning_type >", value, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeGreaterThanOrEqualTo(String value) {
            addCriterion("warning_type >=", value, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeLessThan(String value) {
            addCriterion("warning_type <", value, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeLessThanOrEqualTo(String value) {
            addCriterion("warning_type <=", value, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeLike(String value) {
            addCriterion("warning_type like", value, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeNotLike(String value) {
            addCriterion("warning_type not like", value, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeIn(List<String> values) {
            addCriterion("warning_type in", values, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeNotIn(List<String> values) {
            addCriterion("warning_type not in", values, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeBetween(String value1, String value2) {
            addCriterion("warning_type between", value1, value2, "warningType");
            return (Criteria) this;
        }

        public Criteria andWarningTypeNotBetween(String value1, String value2) {
            addCriterion("warning_type not between", value1, value2, "warningType");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andWarningValueIsNull() {
            addCriterion("warning_value is null");
            return (Criteria) this;
        }

        public Criteria andWarningValueIsNotNull() {
            addCriterion("warning_value is not null");
            return (Criteria) this;
        }

        public Criteria andWarningValueEqualTo(String value) {
            addCriterion("warning_value =", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueNotEqualTo(String value) {
            addCriterion("warning_value <>", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueGreaterThan(String value) {
            addCriterion("warning_value >", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueGreaterThanOrEqualTo(String value) {
            addCriterion("warning_value >=", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueLessThan(String value) {
            addCriterion("warning_value <", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueLessThanOrEqualTo(String value) {
            addCriterion("warning_value <=", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueLike(String value) {
            addCriterion("warning_value like", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueNotLike(String value) {
            addCriterion("warning_value not like", value, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueIn(List<String> values) {
            addCriterion("warning_value in", values, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueNotIn(List<String> values) {
            addCriterion("warning_value not in", values, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueBetween(String value1, String value2) {
            addCriterion("warning_value between", value1, value2, "warningValue");
            return (Criteria) this;
        }

        public Criteria andWarningValueNotBetween(String value1, String value2) {
            addCriterion("warning_value not between", value1, value2, "warningValue");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdIsNull() {
            addCriterion("device_node_id is null");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdIsNotNull() {
            addCriterion("device_node_id is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdEqualTo(String value) {
            addCriterion("device_node_id =", value, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdNotEqualTo(String value) {
            addCriterion("device_node_id <>", value, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdGreaterThan(String value) {
            addCriterion("device_node_id >", value, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdGreaterThanOrEqualTo(String value) {
            addCriterion("device_node_id >=", value, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdLessThan(String value) {
            addCriterion("device_node_id <", value, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdLessThanOrEqualTo(String value) {
            addCriterion("device_node_id <=", value, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdLike(String value) {
            addCriterion("device_node_id like", value, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdNotLike(String value) {
            addCriterion("device_node_id not like", value, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdIn(List<String> values) {
            addCriterion("device_node_id in", values, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdNotIn(List<String> values) {
            addCriterion("device_node_id not in", values, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdBetween(String value1, String value2) {
            addCriterion("device_node_id between", value1, value2, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andDeviceNodeIdNotBetween(String value1, String value2) {
            addCriterion("device_node_id not between", value1, value2, "deviceNodeId");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNull() {
            addCriterion("submit_time is null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNotNull() {
            addCriterion("submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeEqualTo(String value) {
            addCriterion("submit_time =", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotEqualTo(String value) {
            addCriterion("submit_time <>", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThan(String value) {
            addCriterion("submit_time >", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThanOrEqualTo(String value) {
            addCriterion("submit_time >=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThan(String value) {
            addCriterion("submit_time <", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThanOrEqualTo(String value) {
            addCriterion("submit_time <=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLike(String value) {
            addCriterion("submit_time like", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotLike(String value) {
            addCriterion("submit_time not like", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIn(List<String> values) {
            addCriterion("submit_time in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotIn(List<String> values) {
            addCriterion("submit_time not in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeBetween(String value1, String value2) {
            addCriterion("submit_time between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotBetween(String value1, String value2) {
            addCriterion("submit_time not between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andLimitValueIsNull() {
            addCriterion("limit_value is null");
            return (Criteria) this;
        }

        public Criteria andLimitValueIsNotNull() {
            addCriterion("limit_value is not null");
            return (Criteria) this;
        }

        public Criteria andLimitValueEqualTo(String value) {
            addCriterion("limit_value =", value, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueNotEqualTo(String value) {
            addCriterion("limit_value <>", value, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueGreaterThan(String value) {
            addCriterion("limit_value >", value, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueGreaterThanOrEqualTo(String value) {
            addCriterion("limit_value >=", value, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueLessThan(String value) {
            addCriterion("limit_value <", value, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueLessThanOrEqualTo(String value) {
            addCriterion("limit_value <=", value, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueLike(String value) {
            addCriterion("limit_value like", value, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueNotLike(String value) {
            addCriterion("limit_value not like", value, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueIn(List<String> values) {
            addCriterion("limit_value in", values, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueNotIn(List<String> values) {
            addCriterion("limit_value not in", values, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueBetween(String value1, String value2) {
            addCriterion("limit_value between", value1, value2, "limitValue");
            return (Criteria) this;
        }

        public Criteria andLimitValueNotBetween(String value1, String value2) {
            addCriterion("limit_value not between", value1, value2, "limitValue");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasIsNull() {
            addCriterion("device_type_alias is null");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasIsNotNull() {
            addCriterion("device_type_alias is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasEqualTo(String value) {
            addCriterion("device_type_alias =", value, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasNotEqualTo(String value) {
            addCriterion("device_type_alias <>", value, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasGreaterThan(String value) {
            addCriterion("device_type_alias >", value, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasGreaterThanOrEqualTo(String value) {
            addCriterion("device_type_alias >=", value, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasLessThan(String value) {
            addCriterion("device_type_alias <", value, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasLessThanOrEqualTo(String value) {
            addCriterion("device_type_alias <=", value, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasLike(String value) {
            addCriterion("device_type_alias like", value, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasNotLike(String value) {
            addCriterion("device_type_alias not like", value, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasIn(List<String> values) {
            addCriterion("device_type_alias in", values, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasNotIn(List<String> values) {
            addCriterion("device_type_alias not in", values, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasBetween(String value1, String value2) {
            addCriterion("device_type_alias between", value1, value2, "deviceTypeAlias");
            return (Criteria) this;
        }

        public Criteria andDeviceTypeAliasNotBetween(String value1, String value2) {
            addCriterion("device_type_alias not between", value1, value2, "deviceTypeAlias");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table sys_early_record
     *
     * @mbggenerated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table sys_early_record
     *
     * @mbggenerated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}