package com.jorchi.mqtt.entity;

import java.util.ArrayList;
import java.util.List;

public class SysTaskExecuteLogActionExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public SysTaskExecuteLogActionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdIsNull() {
            addCriterion("execute_log_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdIsNotNull() {
            addCriterion("execute_log_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdEqualTo(Integer value) {
            addCriterion("execute_log_id =", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdNotEqualTo(Integer value) {
            addCriterion("execute_log_id <>", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdGreaterThan(Integer value) {
            addCriterion("execute_log_id >", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("execute_log_id >=", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdLessThan(Integer value) {
            addCriterion("execute_log_id <", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdLessThanOrEqualTo(Integer value) {
            addCriterion("execute_log_id <=", value, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdIn(List<Integer> values) {
            addCriterion("execute_log_id in", values, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdNotIn(List<Integer> values) {
            addCriterion("execute_log_id not in", values, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdBetween(Integer value1, Integer value2) {
            addCriterion("execute_log_id between", value1, value2, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andExecuteLogIdNotBetween(Integer value1, Integer value2) {
            addCriterion("execute_log_id not between", value1, value2, "executeLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdIsNull() {
            addCriterion("action_log_id is null");
            return (Criteria) this;
        }

        public Criteria andActionLogIdIsNotNull() {
            addCriterion("action_log_id is not null");
            return (Criteria) this;
        }

        public Criteria andActionLogIdEqualTo(Long value) {
            addCriterion("action_log_id =", value, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdNotEqualTo(Long value) {
            addCriterion("action_log_id <>", value, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdGreaterThan(Long value) {
            addCriterion("action_log_id >", value, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdGreaterThanOrEqualTo(Long value) {
            addCriterion("action_log_id >=", value, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdLessThan(Long value) {
            addCriterion("action_log_id <", value, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdLessThanOrEqualTo(Long value) {
            addCriterion("action_log_id <=", value, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdIn(List<Long> values) {
            addCriterion("action_log_id in", values, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdNotIn(List<Long> values) {
            addCriterion("action_log_id not in", values, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdBetween(Long value1, Long value2) {
            addCriterion("action_log_id between", value1, value2, "actionLogId");
            return (Criteria) this;
        }

        public Criteria andActionLogIdNotBetween(Long value1, Long value2) {
            addCriterion("action_log_id not between", value1, value2, "actionLogId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table sys_task_execute_log_action
     *
     * @mbggenerated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}