package com.jorchi;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableScheduling;
import pdfc.claim.common.CommonUtil;


@EnableCaching
@EnableScheduling
// 不用 jackson 解析 json, 改为 gson 解析
@SpringBootApplication(exclude = {JacksonAutoConfiguration.class})
@ServletComponentScan
@Slf4j
public class ApplicationFast {

    public static void main(String[] args) {
        try {
            SpringApplication application = new SpringApplication(ApplicationFast.class);
            // 允许循环依赖 cycle
            application.setAllowCircularReferences(Boolean.TRUE);
            application.run(args);
            log.info("system started ok.");
        } catch (Throwable e) {
            log.error(CommonUtil.append("system start failed!!!", e.getMessage()));
            e.printStackTrace();
        }
    }
}
