package com.jorchi.project.system.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jorchi.business.dao.NsRegionDao;
import com.jorchi.common.constant.HttpStatus;
import com.jorchi.common.constant.UserConstants;
import com.jorchi.common.exception.CustomException;
import com.jorchi.common.utils.SecurityUtils;
import com.jorchi.common.utils.StringUtils;
import com.jorchi.common.utils.bean.BeanUtils;
import com.jorchi.framework.aspectj.lang.annotation.DataScope;
import com.jorchi.framework.web.domain.TreeSelect;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.project.system.dao.SysRoleDao;
import com.jorchi.project.system.domain.SysDept;
import com.jorchi.project.system.dao.SysDeptDao;
import com.jorchi.project.system.domain.SysRole;
import com.jorchi.project.system.form.SysDeptForm;
import com.jorchi.project.system.vo.SysDeptVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl
{
    @Resource
    private SysDeptDao deptMapper;

    @Resource
    private SysRoleDao roleMapper;

    @Resource
    private MaxIdServiceImpl maxIdService;

    @Resource
    NsRegionDao nsRegionDao;
    @Autowired
    private SysDeptDao sysDeptDao;

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept)
    {
        return deptMapper.selectDeptList(dept,getDeptIds());
    }

    /**
     * 分页查询所有农场
     *
     * @param deptForm 查询条件
     * @return
     */
    public TableDataInfo listPage(SysDeptForm deptForm)
    {
        PageHelper.startPage(deptForm.getPageNum(), deptForm.getPageSize());
        SysDept dept = new SysDept();
        dept.setDeptName(dept.getDeptName());
        return fillAccessDeptNames(deptMapper.selectDeptList(dept,getDeptIds()));

    }

    /**
     * 获取用户部门权限，如果是管理员，则忽略权限
     * @return
     */
    private List<Long> getDeptIds(){
        List<Long> deptIds = null;
        boolean admin = SecurityUtils.getLoginUser().getUser().isAdmin();

        if (!admin){
            deptIds = SecurityUtils.getLoginUser().getDeptIds();
        }
        return deptIds;
    }
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptListVo(SysDept dept)
    {

        return deptMapper.selectDeptList(dept,getDeptIds());
    }

    /**
     * 根据部门列表填充可访问部门名称
     * 此方法首先检查输入的部门列表是否为空，如果为空，则返回null
     * 然后，它将部门列表转换为SysDeptVo对象列表，并尝试获取每个部门的可访问部门名称
     * 最后，它将这些名称以逗号分隔的字符串形式设置到SysDeptVo对象中
     *
     * @param depts 部门列表，如果为空，则返回null
     * @return 填充了可访问部门名称的SysDeptVo对象列表，如果输入为空，则返回null
     */
    private TableDataInfo fillAccessDeptNames(List<SysDept> depts){

        if (depts == null){
            return null;
        }
        // 将SysDept对象列表转换为SysDeptVo对象列表，并复制属性
        List<SysDeptVo> vos = depts
                .stream()
                .map(e->{
                    SysDeptVo vo = new SysDeptVo();
                    BeanUtils.copyBeanProp(vo,e);
                    return  vo;
                }).collect(Collectors.toList());

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(vos);
        rspData.setTotal(new PageInfo(depts).getTotal());

        // 获取所有部门的可访问部门ID列表，并将其从JSON字符串解析为Long型列表
        List<Long> deptIdAccessList = depts.stream()
                .map(SysDept::getAccessDeptIds)
                .filter(e->!CommonUtil.isEmpty(e))
                .map(e-> JSON.parseArray(e,Long.class))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        // 如果可访问部门ID列表不为空，则获取这些部门的详细信息，并创建部门ID到部门对象的映射
        if (!CollectionUtils.isEmpty(deptIdAccessList)){
            List<SysDept> sysDepts = sysDeptDao.selectByIds(deptIdAccessList);
            Map<Long, SysDept> deptMap = sysDepts
                    .stream()
                    .collect(Collectors.toMap(SysDept::getDeptId, e -> e));
            // 填充可访问部门名称
            vos.forEach(e->{
                if (e.getAccessDeptIds() != null){
                    List<Long> accessDeptIds = JSON.parseArray(e.getAccessDeptIds(),Long.class);
                    // 根据可访问部门ID列表获取部门名称，并将其设置到SysDeptVo对象中
                    List<String> accessDeptNames = accessDeptIds.stream()
                            .map(deptMap::get)
                            .map(SysDept::getDeptName)
                            .collect(Collectors.toList());
                    e.setAccessDeptNames(StringUtils.join(accessDeptNames,","));
                }
            });

        }
        // 返回填充了可访问部门名称的SysDeptVo对象列表
        return rspData;
    }
    /**
     * 省市区查询
     * 省：parent_id = 100
     * 市：parent_id = 省code
     * 区： parent_id = 市code
     *
     * @author: Sugar.Tan
     * @date: 2021-04-29 18:09
     */
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptByParentId(long parentId)
    {
        return deptMapper.selectDeptByParentId(parentId);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts)
    {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = new ArrayList<Long>();
        for (SysDept dept : depts)
        {
            tempList.add(dept.getDeptId());
        }
        for (Iterator<SysDept> iterator = depts.iterator(); iterator.hasNext();)
        {
            SysDept dept = (SysDept) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId()))
            {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts)
    {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Long> selectDeptListByRoleId(Long roleId)
    {
        SysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.getDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept selectDeptById(Long deptId)
    {
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId)
    {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId)
    {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0 ? true : false;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId)
    {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0 ? true : false;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    public String checkDeptNameUnique(SysDept dept)
    {
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDept dept)
    {
//        SysDept info = deptMapper.selectDeptById(dept.getParentId());
//        if (info == null)
//            throw new CustomException("请先选择父级部门，再新增!");

        // 如果父节点不为正常状态,则不允许新增子节点
//        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus()))
//        {
//            throw new CustomException("部门停用，不允许新增");
//        }
//        dept.setAncestors(CommonUtil.append(info.getAncestors(), ",", dept.getParentId()));
        return deptMapper.insertDept(dept);
    }

    /**
     * 修改保存部门信息
     */
    public int updateDept(SysDept dept)
    {
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept))
        {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        int result = deptMapper.updateDept(dept);

        // 如果该部门是启用状态，则启用该部门的所有上级部门
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) &&
                dept.getAncestors() != null &&
                dept.getAncestors().length() > 0)
        {
            updateParentDeptStatus(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatus(SysDept dept)
    {
        String updateBy = dept.getUpdateBy();
        dept = deptMapper.selectDeptById(dept.getDeptId());
        dept.setUpdateBy(updateBy);
        String[]  ancestors =  dept.getAncestors().split(",");
        List<String> list = new ArrayList<String>();
        for (int i=0;i<ancestors.length;i++){
            list.add(ancestors[i]);
        }
        dept.setAncestorsList(list);
        deptMapper.updateDeptStatus(dept);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId 被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors)
    {
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children)
        {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId)
    {
        // 部门ID = - 原ID+99+maxIdService.nextId
        String deletedId = CommonUtil.append('-', deptId, 99, maxIdService.getAndIncrement("deletedId"));
        return deptMapper.deleteDeptById(deptId, Long.valueOf(deletedId));
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t)
    {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t)
    {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext())
        {
            SysDept n = (SysDept) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue())
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t)
    {
        return getChildList(list, t).size() > 0 ? true : false;
    }

    /**
     * 部门名称信息
     */
    public List<SysDeptVo> deptNameList(){
         List<SysDeptVo> list = deptMapper.deptNameList();
         return list;
    }
}
