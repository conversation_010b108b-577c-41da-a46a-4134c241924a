package com.jorchi.project.monitor.api;

import com.jorchi.common.utils.poi.ExcelUtil;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.project.monitor.domain.SysOperLog;
import com.jorchi.project.monitor.service.SysOperLogServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.CommonUtil;

import java.util.List;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/operlog")
// // @Api(tags = {"操作日志记录"})
public class SysOperlogApi extends BaseApi
{
    @Autowired
    private SysOperLogServiceImpl operLogService;

    @PreAuthorize("@ss.hasPermi('monitor:operlog:list')")
    @GetMapping("/list")
    // // @ApiOperation("操作日志-列表")
    public TableDataInfo list(SysOperLog operLog)
    {
        // 结束时间加一天
        String endTime = (String) operLog.getParams().get("endTime");
        if (endTime != null && endTime.length() == 10)
            operLog.getParams().put("endTime", CommonUtil.append(endTime, " 23:59:59"));

        startPage();
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        return getDataTable(list);
    }

    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:export')")
    @GetMapping("/export")
    // // @ApiOperation("操作日志-导出")
    public AjaxResult export(SysOperLog operLog)
    {
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        ExcelUtil<SysOperLog> util = new ExcelUtil<SysOperLog>(SysOperLog.class);
        return util.exportExcel(list, "操作日志");
    }

    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @PostMapping("delete/{operIds}")
    // // @ApiOperation("操作日志-清除--加了前缀delete/方式Post")
    public AjaxResult remove(@PathVariable Long[] operIds)
    {
        return toAjax(operLogService.deleteOperLogByIds(operIds));
    }

    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @PreAuthorize("@ss.hasPermi('monitor:operlog:remove')")
    @PostMapping("/clean")
    // // @ApiOperation("操作日志-清楚--改方式为post")
    public AjaxResult clean()
    {
        operLogService.cleanOperLog();
        return AjaxResult.success();
    }
}
