package com.jorchi.business.api;

import com.jorchi.business.po.NsProdAudit;
import com.jorchi.business.service.NsProdAuditService;
import com.jorchi.business.form.NsProdAuditForm;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;

/**
 * 表ns_prod_audit的控制层对象<br/>
 * 对应表名：ns_prod_audit，表备注：生产日常稽核表
 * @author: xubinbin at jorchi
 * @date: 2023-05-11 16:09:21
 */
@Slf4j
@RestController
@RequestMapping("/nsProdAudit")
public class NsProdAuditApi extends BaseApi {

    @Resource
    NsProdAuditService nsProdAuditService;


    /**
     * 按主键查询（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @PreAuthorize("@ss.hasPermi('business:nsProdAudit:list')")
    @PostMapping("/nsProdAuditDtl") // {{baseApi}}/nsProdAudit/nsProdAuditDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long id = idNameVo.getId();
            // id can not be empty
            if (id == null)
                throw ClientException.of("findById but id can not be empty error!");

            NsProdAudit bean = nsProdAuditService.findById(id);
            if (bean == null)
                throw ClientException.of("NsProdAudit not found error!");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsProdAuditDtl error!", t);
            throw t;
        }
    }

    /**
     * 分页查询（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @PreAuthorize("@ss.hasPermi('business:nsProdAudit:list')")
    @PostMapping("/nsProdAuditList") // {{baseApi}}/nsProdAudit/nsProdAuditList
    public TableDataInfo listPage(@RequestBody NsProdAuditForm nsProdAuditVo) {
        try {
            // 分页参数检查
            if (nsProdAuditVo.getPageNum() == null) {
                log.warn("/nsProdAuditList please set pageNum!!");
                nsProdAuditVo.setPageNum(1);
            }
            if (nsProdAuditVo.getPageSize() == null) {
                log.warn("/nsProdAuditList please set pageSize!!");
                nsProdAuditVo.setPageSize(10);
            }

            return getDataTable(nsProdAuditService.listPage(nsProdAuditVo));
        } catch (Throwable t) {
            log.error("nsProdAuditList listPage error!", t);
            throw t;
        }
    }

    /**
     * 删除（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @PreAuthorize("@ss.hasPermi('business:nsProdAudit:remove')")
    @PostMapping("/nsProdAuditDelete") // {{baseApi}}/nsProdAudit/nsProdAuditDelete
    @Log(title = "生产日常稽核表", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsProdAudit(@RequestBody IdNameVo idNameVo) {
        try {
            Long id = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsProdAudit/nsProdAuditDelete start, id:", id));
            return AjaxResult.success(nsProdAuditService.deleteNsProdAudit(id));
        } catch (Throwable t) {
            log.error("nsProdAuditDelete error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（生产日常稽核表）<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @PreAuthorize("@ss.hasPermi('business:nsProdAudit:edit')")
    @PostMapping("/nsProdAuditSave") // {{baseApi}}/nsProdAudit/nsProdAuditSave
    @Log(title = "生产日常稽核表", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsProdAudit (@RequestBody NsProdAudit nsProdAudit) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsProdAudit/nsProdAuditSave start, RequestBody:", nsProdAudit));

            // 检查待保存数据的合法性
            validatePo(nsProdAudit);

            // TODO : 参数非空性检查 ...

            // 名称唯一性检查?
            // nsProdAuditService.checkNameExisted(nsProdAudit);

            NsProdAudit result = nsProdAuditService.saveNsProdAudit(nsProdAudit,loginUser);
            return AjaxResult.success(result);
        } catch (Throwable t) {
            log.error("nsProdAuditSave error!", t);
            throw t;
        }
    }

    /**
     * 检查待保存数据的合法性（生产日常稽核表）<br>
     * @author:  xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    protected void validatePo(NsProdAudit nsProdAudit) {

        // 种植区名称-长度检查
        if (nsProdAudit.getRegionName() != null && nsProdAudit.getRegionName().length() > 50)
            throw ClientException.of("【种植区名称】字段最多只能输入【50】个字符！");

        // 大棚编号-长度检查
        if (nsProdAudit.getHouseName() != null && nsProdAudit.getHouseName().length() > 50)
            throw ClientException.of("【大棚编号】字段最多只能输入【50】个字符！");

        // 外观-长度检查
        if (nsProdAudit.getAspect() != null && nsProdAudit.getAspect().length() > 50)
            throw ClientException.of("【外观】字段最多只能输入【50】个字符！");

        // 病害-长度检查
        if (nsProdAudit.getDisease() != null && nsProdAudit.getDisease().length() > 50)
            throw ClientException.of("【病害】字段最多只能输入【50】个字符！");

        // 虫害-长度检查
        if (nsProdAudit.getInsectPest() != null && nsProdAudit.getInsectPest().length() > 50)
            throw ClientException.of("【虫害】字段最多只能输入【50】个字符！");

        // 棚膜-长度检查
        if (nsProdAudit.getCanopyFilm() != null && nsProdAudit.getCanopyFilm().length() > 50)
            throw ClientException.of("【棚膜】字段最多只能输入【50】个字符！");

        // 大门-长度检查
        if (nsProdAudit.getDoor() != null && nsProdAudit.getDoor().length() > 50)
            throw ClientException.of("【大门】字段最多只能输入【50】个字符！");

        // 异常说明-长度检查
        if (nsProdAudit.getExceptionDescription() != null && nsProdAudit.getExceptionDescription().length() > 50)
            throw ClientException.of("【异常说明】字段最多只能输入【50】个字符！");

        // TODO : 其它必填项检查？
        // if (releaseVersion.getName() == null || releaseVersion.getName().trim().isEmpty())
        //     throw ClientException.of("【name】字段不能为空！");
    }

}
