package com.jorchi.business.api;

import com.jorchi.business.po.NsProductStockItem;
import com.jorchi.business.service.NsProductStockItemService;
import com.jorchi.business.form.NsProductStockItemForm;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;

/**
 * 表ns_product_stock_item的控制层对象<br/>
 * 对应表名：ns_product_stock_item，表备注：农产品库存项目表
 * @author: 周建宇 at jorchi
 * @date: 2025-06-14 16:04:47
 */
@Slf4j
@RestController
@RequestMapping("/nsProductStockItem")
public class NsProductStockItemApi extends BaseApi {

    @Resource
    NsProductStockItemService nsProductStockItemService;


    /**
     * 按主键查询（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsProductStock:list')")
    @PostMapping("/nsProductStockItemDtl") // {{baseApi}}/nsProductStockItem/nsProductStockItemDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long itemId = idNameVo.getId();
            // id can not be empty
            if (itemId == null)
                throw ClientException.of("findById but itemId can not be empty error!");

            NsProductStockItem bean = nsProductStockItemService.findById(itemId);
            if (bean == null)
                throw ClientException.of("NsProductStockItem not found error!");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsProductStockItemDtl error!", t);
            throw t;
        }
    }

    /**
     * 分页查询（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsProductStock:list')")
    @PostMapping("/nsProductStockItemList") // {{baseApi}}/nsProductStockItem/nsProductStockItemList
    public TableDataInfo listPage(@RequestBody NsProductStockItemForm nsProductStockItemVo) {
        try {
            // 分页参数检查
            if (nsProductStockItemVo.getPageNum() == null) {
                log.warn("/nsProductStockItemList please set pageNum!!");
                nsProductStockItemVo.setPageNum(1);
            }
            if (nsProductStockItemVo.getPageSize() == null) {
                log.warn("/nsProductStockItemList please set pageSize!!");
                nsProductStockItemVo.setPageSize(10);
            }

            return getDataTable(nsProductStockItemService.listPage(nsProductStockItemVo));
        } catch (Throwable t) {
            log.error("nsProductStockItemList listPage error!", t);
            throw t;
        }
    }

    /**
     * 删除（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsProductStockItem:remove')")
    @PostMapping("/nsProductStockItemDelete") // {{baseApi}}/nsProductStockItem/nsProductStockItemDelete
    @Log(title = "农产品库存项目表", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsProductStockItem(@RequestBody IdNameVo idNameVo) {
        try {
            Long itemId = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsProductStockItem/nsProductStockItemDelete start, itemId:", itemId));
            return AjaxResult.success(nsProductStockItemService.deleteNsProductStockItem(itemId));
        } catch (Throwable t) {
            log.error("nsProductStockItemDelete error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（农产品库存项目表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsProductStockItem:edit')")
    @PostMapping("/nsProductStockItemSave") // {{baseApi}}/nsProductStockItem/nsProductStockItemSave
    @Log(title = "农产品库存项目表", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsProductStockItem (@RequestBody NsProductStockItem nsProductStockItem) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsProductStockItem/nsProductStockItemSave start, RequestBody:", nsProductStockItem));

            // 检查待保存数据的合法性
            validatePo(nsProductStockItem);

            // TODO : 参数非空性检查 ...

            // 名称唯一性检查?
            // nsProductStockItemService.checkNameExisted(nsProductStockItem);

            NsProductStockItem result = nsProductStockItemService.saveNsProductStockItem(nsProductStockItem);
            return AjaxResult.success(result);
        } catch (Throwable t) {
            log.error("nsProductStockItemSave error!", t);
            throw t;
        }
    }

    /**
     * 检查待保存数据的合法性（农产品库存项目表）<br>
     * @author:  周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    protected void validatePo(NsProductStockItem nsProductStockItem) {

        // 农产品类别-长度检查
        if (nsProductStockItem.getCropType() != null && nsProductStockItem.getCropType().length() > 64)
            throw ClientException.of("【农产品类别】字段最多只能输入【64】个字符！");

        // 科别-长度检查
        if (nsProductStockItem.getCropCategory() != null && nsProductStockItem.getCropCategory().length() > 64)
            throw ClientException.of("【科别】字段最多只能输入【64】个字符！");

        // 品种-长度检查
        if (nsProductStockItem.getBreeds() != null && nsProductStockItem.getBreeds().length() > 64)
            throw ClientException.of("【品种】字段最多只能输入【64】个字符！");

        // 农产品名称-长度检查
        if (nsProductStockItem.getCropName() != null && nsProductStockItem.getCropName().length() > 64)
            throw ClientException.of("【农产品名称】字段最多只能输入【64】个字符！");

        // 规格-长度检查
        if (nsProductStockItem.getSpec() != null && nsProductStockItem.getSpec().length() > 16)
            throw ClientException.of("【规格】字段最多只能输入【16】个字符！");

        // TODO : 其它必填项检查？
        // if (releaseVersion.getName() == null || releaseVersion.getName().trim().isEmpty())
        //     throw ClientException.of("【name】字段不能为空！");
    }

}
