package com.jorchi.business.api;

import com.jorchi.business.form.FinishProcessTaskForm;
import com.jorchi.business.po.NsApprovalProcess;
import com.jorchi.business.service.NsApprovalProcessService;
import com.jorchi.business.form.NsApprovalProcessForm;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;

/**
 * 表ns_approval_process的控制层对象<br/>
 * 对应表名：ns_approval_process，表备注：审批流程表
 *
 * @author: 周建宇 at jorchi
 * @date: 2024-12-25 15:16:03
 */
@Slf4j
@RestController
@RequestMapping("/nsApprovalProcess")
public class NsApprovalProcessApi extends BaseApi {

    @Resource
    NsApprovalProcessService nsApprovalProcessService;


    /**
     * 按主键查询（审批流程表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @PreAuthorize("@ss.hasPermi('business:nsApprovalProcess:list')")
    @PostMapping("/nsApprovalProcessDtl") // {{baseApi}}/nsApprovalProcess/nsApprovalProcessDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long approvalId = idNameVo.getId();
            // id can not be empty
            if (approvalId == null)
                throw ClientException.of("findById but approvalId can not be empty error!");

            NsApprovalProcess bean = nsApprovalProcessService.findById(approvalId);
            if (bean == null)
                throw ClientException.of("NsApprovalProcess not found error!");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsApprovalProcessDtl error!", t);
            throw t;
        }
    }

    /**
     * 分页查询（审批流程表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @PreAuthorize("@ss.hasPermi('business:nsApprovalProcess:list')")
    @PostMapping("/nsApprovalProcessList") // {{baseApi}}/nsApprovalProcess/nsApprovalProcessList
    public TableDataInfo listPage(@RequestBody NsApprovalProcessForm nsApprovalProcessVo) {
        try {
            // 分页参数检查
            if (nsApprovalProcessVo.getPageNum() == null) {
                log.warn("/nsApprovalProcessList please set pageNum!!");
                nsApprovalProcessVo.setPageNum(1);
            }
            if (nsApprovalProcessVo.getPageSize() == null) {
                log.warn("/nsApprovalProcessList please set pageSize!!");
                nsApprovalProcessVo.setPageSize(10);
            }

            return getDataTable(nsApprovalProcessService.listPage(nsApprovalProcessVo));
        } catch (Throwable t) {
            log.error("nsApprovalProcessList listPage error!", t);
            throw t;
        }
    }


    /**
     * 根据businessId 查询审批流程
     * <AUTHOR> at jorchi
     * @date: 2024-12-27 15:16:03
     */
//    @PreAuthorize("@ss.hasPermi('business:nsApprovalProcess:list')")
    @PostMapping("/getByBusinessId") // {{baseApi}}/nsApprovalProcess/nsApprovalProcessList
    public AjaxResult getByBusinessId(@RequestBody NsApprovalProcessForm nsApprovalProcessVo) {
        try {
            Long businessId =
                    nsApprovalProcessVo.getBusinessId();
            String businessType = nsApprovalProcessVo.getBusinessType();

            if (businessId == null || businessType == null){
                throw ClientException.of("nsApprovalProcessListByBusinessId but businessId or businessType can not be empty error!");
            }
            return AjaxResult.success(nsApprovalProcessService
                    .getByBusinessId(businessId,businessType));
        }catch (Throwable t){
            log.error("nsApprovalProcessListByBusinessId error!", t);
            throw t;
        }
    }

    /**
     *  完成流程任务
     *  @author: 周建宇 at jorchi
     *  @date: 2024-12-27 15:16:03
     */
//    @PreAuthorize("@ss.hasPermi('business:nsApprovalProcess:edit')")
    @PostMapping("/finishProcessTask") // {{baseApi}}/nsApprovalProcess/finishProcessTask
    @Log(title = "审批流程表", businessType = BusinessType.UPDATE)
    public AjaxResult finishProcessTask(@RequestBody FinishProcessTaskForm form) {
        try {
            log.info(CommonUtil.append("/nsApprovalProcess/finishProcessTask start, form:", form));
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            return AjaxResult.success(nsApprovalProcessService.finishProcessTask(form,loginUser,false));
        } catch (Throwable t) {
            log.error("finishProcessTask error!", t);
            throw t;
        }
    }

    /**
     * 删除（审批流程表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @PreAuthorize("@ss.hasPermi('business:nsApprovalProcess:remove')")
    @PostMapping("/nsApprovalProcessDelete") // {{baseApi}}/nsApprovalProcess/nsApprovalProcessDelete
    @Log(title = "审批流程表", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsApprovalProcess(@RequestBody IdNameVo idNameVo) {
        try {
            Long approvalId = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsApprovalProcess/nsApprovalProcessDelete start, approvalId:", approvalId));
            return AjaxResult.success(nsApprovalProcessService.deleteNsApprovalProcess(approvalId));
        } catch (Throwable t) {
            log.error("nsApprovalProcessDelete error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（审批流程表）<br>
     * ID 为空即为新增，否则为更新
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @PreAuthorize("@ss.hasPermi('business:nsApprovalProcess:edit')")
    @PostMapping("/nsApprovalProcessSave") // {{baseApi}}/nsApprovalProcess/nsApprovalProcessSave
    @Log(title = "审批流程表", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsApprovalProcess(@RequestBody NsApprovalProcess nsApprovalProcess) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsApprovalProcess/nsApprovalProcessSave start, RequestBody:", nsApprovalProcess));

            // 检查待保存数据的合法性
            validatePo(nsApprovalProcess);

            // TODO : 参数非空性检查 ...

            // 名称唯一性检查?
            // nsApprovalProcessService.checkNameExisted(nsApprovalProcess);

            NsApprovalProcess result = nsApprovalProcessService.saveNsApprovalProcess(nsApprovalProcess);
            return AjaxResult.success(result);
        } catch (Throwable t) {
            log.error("nsApprovalProcessSave error!", t);
            throw t;
        }
    }

    /**
     * 检查待保存数据的合法性（审批流程表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    protected void validatePo(NsApprovalProcess nsApprovalProcess) {

        // 发起人-长度检查
        if (nsApprovalProcess.getInitiator() != null && nsApprovalProcess.getInitiator().length() > 128)
            throw ClientException.of("【发起人】字段最多只能输入【128】个字符！");

        // 业务类型-长度检查
        if (nsApprovalProcess.getBusinessType() != null && nsApprovalProcess.getBusinessType().length() > 64)
            throw ClientException.of("【业务类型】字段最多只能输入【64】个字符！");

        // 审批内容-长度检查
        if (nsApprovalProcess.getApprovalContent() != null && nsApprovalProcess.getApprovalContent().length() > 65535)
            throw ClientException.of("【审批内容】字段最多只能输入【65,535】个字符！");

        // 审批人-长度检查
        if (nsApprovalProcess.getApprover() != null && nsApprovalProcess.getApprover().length() > 128)
            throw ClientException.of("【审批人】字段最多只能输入【128】个字符！");

        // TODO : 其它必填项检查？
        // if (releaseVersion.getName() == null || releaseVersion.getName().trim().isEmpty())
        //     throw ClientException.of("【name】字段不能为空！");
    }

}
