package com.jorchi.business.api;

import com.jorchi.common.utils.CommonUtil;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.mqtt.service.RestartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: xubinbin
 * @Date: 2023/4/18 10:01
 * @Describe: 测试看门狗服务controller
 */
@Slf4j
@RestController
public class RestartTestApi {

    /**
     * 测试看门口是否能正常运行
     * 时间：2023年4月18日10:02:10
     * @param nodeName
     * @return
     */
    // curl http://localhost:8000/server-api/test/restartByWathDogTest
    @GetMapping("/test/restartByWathDogTest")
    public AjaxResult restartByWathDogTest(@RequestParam(required = false) String nodeName) {
        log.warn(CommonUtil.append("restartByWathDogTest by api, do restartByWathDog..."));
        RestartService.getInstance().restartByWathDog();
        return AjaxResult.success();
    }
}
