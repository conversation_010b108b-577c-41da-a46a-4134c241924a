package com.jorchi.business.api;

import com.jorchi.business.form.SavePurchaseStockIn;
import com.jorchi.business.po.NsStockIn;
import com.jorchi.business.service.NsStockInService;
import com.jorchi.business.form.NsStockInForm;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;

/**
 * 表ns_stock_in的控制层对象<br/>
 * 对应表名：ns_stock_in，表备注：入库记录表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-02 09:19:01
 */
@Slf4j
@RestController
@RequestMapping("/nsStockIn")
public class NsStockInApi extends BaseApi {

    @Resource
    NsStockInService nsStockInService;


    /**
     * 按主键查询（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @PreAuthorize("@ss.hasPermi('business:nsStockIn:list')")
    @PostMapping("/nsStockInDtl") // {{baseApi}}/nsStockIn/nsStockInDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long stockInId = idNameVo.getId();
            // id can not be empty
            if (stockInId == null)
                throw ClientException.of("findById but stockInId can not be empty error!");

            NsStockIn bean = nsStockInService.findById(stockInId);
            if (bean == null)
                throw ClientException.of("NsStockIn not found error!");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsStockInDtl error!", t);
            throw t;
        }
    }

    /**
     * 分页查询（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @PreAuthorize("@ss.hasPermi('business:nsStockIn:list')")
    @PostMapping("/nsStockInList") // {{baseApi}}/nsStockIn/nsStockInList
    public TableDataInfo listPage(@RequestBody NsStockInForm nsStockInVo) {
        try {
            // 分页参数检查
            if (nsStockInVo.getPageNum() == null) {
                log.warn("/nsStockInList please set pageNum!!");
                nsStockInVo.setPageNum(1);
            }
            if (nsStockInVo.getPageSize() == null) {
                log.warn("/nsStockInList please set pageSize!!");
                nsStockInVo.setPageSize(10);
            }

            return getDataTable(nsStockInService.listPage(nsStockInVo));
        } catch (Throwable t) {
            log.error("nsStockInList listPage error!", t);
            throw t;
        }
    }

    /**
     * 删除（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @PreAuthorize("@ss.hasPermi('business:nsStockIn:remove')")
    @PostMapping("/nsStockInDelete") // {{baseApi}}/nsStockIn/nsStockInDelete
    @Log(title = "入库记录表", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsStockIn(@RequestBody IdNameVo idNameVo) {
        try {
            Long stockInId = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsStockIn/nsStockInDelete start, stockInId:", stockInId));
            return AjaxResult.success(nsStockInService.deleteNsStockIn(stockInId));
        } catch (Throwable t) {
            log.error("nsStockInDelete error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（入库记录表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @PreAuthorize("@ss.hasPermi('business:nsStockIn:edit')")
    @PostMapping("/nsStockInSave") // {{baseApi}}/nsStockIn/nsStockInSave
    @Log(title = "入库记录表", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsStockIn (@RequestBody SavePurchaseStockIn savePurchaseStockIn) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsStockIn/nsStockInSave start, RequestBody:", savePurchaseStockIn));

            // 检查待保存数据的合法性
            validatePo(savePurchaseStockIn);

            nsStockInService.saveNsStockIn(savePurchaseStockIn);
            return AjaxResult.success();
        } catch (Throwable t) {
            log.error("nsStockInSave error!", t);
            throw t;
        }
    }

    /**
     * 检查待保存数据的合法性（入库记录表）<br>
     * @author:  周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    protected void validatePo(SavePurchaseStockIn nsStockIn) {

        // 关联采购单号-长度检查
        if (nsStockIn.getPurchaseCode() != null && nsStockIn.getPurchaseCode().length() > 64)
            throw ClientException.of("【关联采购单号】字段最多只能输入【64】个字符！");

        // 入库仓库编码-长度检查
        if (nsStockIn.getWarehouseCode() != null && nsStockIn.getWarehouseCode().length() > 64)
            throw ClientException.of("【入库仓库编码】字段最多只能输入【64】个字符！");

        // 经办人-长度检查
        if (nsStockIn.getOperator() != null && nsStockIn.getOperator().length() > 128)
            throw ClientException.of("【经办人】字段最多只能输入【128】个字符！");


        // if (releaseVersion.getName() == null || releaseVersion.getName().trim().isEmpty())
        //     throw ClientException.of("【name】字段不能为空！");
        if (nsStockIn.getWarehouseId()==null){
            throw ClientException.of("【入库仓库】字段不能为空！");
        }
        if (nsStockIn.getPurchaseId() == null) {
            throw new ClientException("【关联采购单号】字段不能为空！");
        }
    }

}
