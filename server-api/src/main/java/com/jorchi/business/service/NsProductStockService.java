package com.jorchi.business.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.NsCropBreedsDao;
import com.jorchi.business.dao.NsProductStockDao;
import com.jorchi.business.dao.NsProductionPlanDao;
import com.jorchi.business.form.Convert2ProductFormItem;
import com.jorchi.business.form.NsProductStockForm;
import com.jorchi.business.po.*;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.SecurityUtils;
import com.jorchi.common.utils.StringUtils;
import com.jorchi.common.utils.bean.BeanUtils;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 表ns_product_stock的服务层对象<br/>
 * 对应表名：ns_product_stock，表备注：农产品库存表
 *
 * @author: 周建宇 at jorchi
 * @date: 2024-12-17 15:49:39
 */
@Slf4j
@Service
public class NsProductStockService extends BaseService {

    @Resource
    NsProductStockDao nsProductStockDao;

    @Resource
    MaxIdServiceImpl maxIdService;

    @Resource
    NsProductionPlanDao nsProductionPlanDao;

    @Resource
    NsCropBreedsDao nsCropBreedsDao;

    @Resource
    NsProductStockItemService nsProductstockItemService;


    /**
     * 按主键查询（农产品库存表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    public NsProductStock findById(Long primaryKey) {
        return nsProductStockDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（农产品库存表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    public NsProductStock findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsProductStock query = NsProductStock.builder().cropName(name)
                .deleted(CommonDef.DELETE_FLAG_NORMAL)
                .build();

        // 查询
        List<NsProductStock> result = nsProductStockDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（农产品库存表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    public List<NsProductStock> listPage(NsProductStockForm nsProductStockForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsProductStockForm:", nsProductStockForm));

        // 设置分页参数
        PageHelper.startPage(nsProductStockForm.getPageNum(), nsProductStockForm.getPageSize(), "create_time desc");

        if (StringUtils.isNotEmpty(nsProductStockForm.getOrderByColumn())) {
            PageHelper.orderBy(CommonUtil.append(nsProductStockForm.getOrderByColumn(), " ", nsProductStockForm.getIsAsc()));

        }
        return nsProductStockDao.selectPageByForm(nsProductStockForm);
    }

    /**
     * 查询所有农产品分类（农产品库存表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    public List<NsProductStock> listAll(NsProductStockForm nsProductStockForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsProductStockForm:", nsProductStockForm));

        return nsProductStockDao.selectPageByForm(nsProductStockForm);
    }

    /**
     * 半成品转成品后，成品库存表数据也需同步更新
     *
     * @param nsProduct 成品
     */

    @Transactional(rollbackFor = Exception.class)
    public void addByProduct(NsProduct nsProduct, List<Convert2ProductFormItem> items) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long productionPlanId = nsProduct.getProductionPlanId();
        // 查询生产计划
        NsProductionPlan productionPlan = nsProductionPlanDao.selectByPrimaryKey(productionPlanId);

        Long cropId = productionPlan.getCropId();

        if (cropId == null) {
            log.error("生产计划对应的作物不存在，无法继续");
            return;
        }
        NsCropBreeds nsCropBreeds = nsCropBreedsDao.selectByPrimaryKey(cropId);

        if (nsCropBreeds == null) {
            log.error("生产计划对应的作物不存在，无法继续");
            return;
        }
        if (nsCropBreeds.getProductStockId() == null) {
            log.error("生产计划对应的作物不存在，无法继续");
            return;
        }

        //加锁防止并发问题
        NsProductStock nsProductStock = nsProductStockDao.selectByIdWithLock(nsCropBreeds.getProductStockId());

        if (nsProductStock != null) {
            if (nsProductStock.getStockQuantity() == null) {
                nsProductStock.setStockQuantity(BigDecimal.ZERO);
            }

            if (items != null) {
                List<NsProductStockItem> itemList = new ArrayList<>();
                for (Convert2ProductFormItem item : items) {
                    NsProductStockItem stockItem = new NsProductStockItem();
                    BeanUtils.copyBeanProp(stockItem, nsProductStock);
                    stockItem.setSpec(item.getUnit());
                    stockItem.setNum(item.getNum());
                    stockItem.setWeight(item.getTotalWeight());
                    itemList.add(stockItem);
                }
                nsProductstockItemService.saveBatch(itemList, nsProductStock.getProductStockId());
            }
            //进行库存的累加
            nsProductStock.setStockQuantity(nsProductstockItemService.calculateWeight(nsProductStock.getProductStockId()));

            compareQuantity(nsProductStock);
            nsProductStock.setUpdateBy(loginUser.getUser().getUserId());
            nsProductStock.setUpdateTime(new Date());
            nsProductStockDao.updateByPrimaryKey(nsProductStock);

        }
    }

    /**
     * 计算库存预警状态
     *
     * @param nsProductStock
     */
    private void compareQuantity(NsProductStock nsProductStock) {
        if (nsProductStock.getStockWarningLine() != null) {
            if (nsProductStock.getStockQuantity().compareTo(nsProductStock.getStockWarningLine()) < 0) {
                nsProductStock.setWarningStatus("预警");
            } else {
                nsProductStock.setWarningStatus("正常");
            }
        }
    }

    /**
     * 删除（农产品库存表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    public boolean deleteNsProductStock(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsProductStockDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsProductStock nsProductStock = NsProductStock.builder()
                .productStockId(id)
                .deleted(CommonDef.DELETE_FLAG_DELETE)
                // .status(status)
                .build();

        // 更新数据
        int res = nsProductStockDao.updateSelectiveByPrimaryKey(nsProductStock);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（农产品库存表）<br>
     * ID 为空即为新增，否则为更新
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    public NsProductStock saveNsProductStock(NsProductStock nsProductStock) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long primaryKey = nsProductStock.getProductStockId();

        //比较库存是否预警
        if (nsProductStock.getStockWarningLine() != null && nsProductStock.getStockQuantity() != null) {
            if (nsProductStock.getStockQuantity().compareTo(nsProductStock.getStockWarningLine()) < 0) {
                nsProductStock.setWarningStatus("预警");
            } else {
                nsProductStock.setWarningStatus("正常");
            }
        }
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsProductStock.setProductStockId(maxIdService.getAndIncrement("NsProductStock"));
            nsProductStock.setWarningStatus("");
            // set CreateTime
            nsProductStock.setCreateTime(new Date());
            nsProductStock.setCreateBy(loginUser.getUser().getUserId());

            nsProductStock.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            if (nsProductStock.getStockQuantity() == null) {
                nsProductStock.setStockQuantity(BigDecimal.ZERO);
            }

            // do insert by not null properties
            nsProductStockDao.insertSelective(nsProductStock);
            return nsProductStock;
        } else { // do update

            // set UpdateTime
            nsProductStock.setUpdateTime(new Date());
            nsProductStock.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            // do update by not null properties
            int count = nsProductStockDao.updateSelectiveByPrimaryKey(nsProductStock);
            if (count <= 0)
                nsProductStockDao.insertSelective(nsProductStock);
            return nsProductStock;
        }
    }

    /**
     * 根据ID批量查询农产品
     *
     * @param productStockIds 农产品ID
     * @return
     */
    public List<NsProductStock> selectByIds(List<Long> productStockIds) {
        return nsProductStockDao
                .selectBatchByPrimaryKeys(productStockIds);
    }

    /**
     * 从销售明细中获取的农产品库存数据并进行累加
     *
     * @param nsSalesOrderItems 销售明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBySalesOrderItem(Page<NsSalesOrderItem> nsSalesOrderItems) {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        List<Long> productStockIds = nsSalesOrderItems
                .stream()
                .map(NsSalesOrderItem::getProductStockId)
                .collect(Collectors.toList());

        List<NsProductStock> nsProductStocks = nsProductStockDao.selectByIdsWithLock(productStockIds);

        Map<Long, NsProductStock> productStockMaP = nsProductStocks
                .stream()
                .collect(Collectors.toMap(NsProductStock::getProductStockId, e -> e));

        for (NsSalesOrderItem nsSalesOrderItem : nsSalesOrderItems) {
            NsProductStock nsProductStock = productStockMaP.get(nsSalesOrderItem.getProductStockId());

            if (nsProductStock != null) {

                //如果没有库存，尝试初始化为0
                if (nsProductStock.getStockQuantity() == null) {
                    nsProductStock.setStockQuantity(BigDecimal.ZERO);
                }
                nsProductStock.setStockQuantity(nsProductStock.getStockQuantity().add(nsSalesOrderItem.getQuantity()));

                if (nsProductStock.getStockQuantity().compareTo(BigDecimal.ZERO) < 0) {
                    throw ClientException.of("库存不足，无法继续操作");
                }

                compareQuantity(nsProductStock);

                nsProductStock.setUpdateBy(loginUser.getUser().getUserId());
                nsProductStock.setUpdateTime(new Date());

                nsProductStockDao.updateByPrimaryKey(nsProductStock);
            }
        }

    }

    /**
     * 重名检查(请按需使用)（农产品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
    public void checkNameExisted(NsProductStock nsProductStock) {
    // 设置查询条件：名称，删除标志位等
    NsProductStock query = NsProductStock.builder().name(nsProductStock.getName()).build();
    List<NsProductStock> result = nsProductStockDao.selectPage(query);
    if (result == null || result.isEmpty())
    return;

    // 新增或主键不同
    if (nsProductStock.getProductStockId() == null || !result.get(0).getProductStockId().equals(nsProductStock.getProductStockId()))
    throw ClientException.of("该名称已存在！Code:400-", nsProductStock.getName(), "-k-", result.get(0).getProductStockId());
    }*/

}
