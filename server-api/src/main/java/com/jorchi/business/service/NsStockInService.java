package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.NsPurchaseApplicationItemDao;
import com.jorchi.business.dao.NsStockInDao;
import com.jorchi.business.form.NsStockInForm;
import com.jorchi.business.form.SavePurchaseStockIn;
import com.jorchi.business.po.*;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.SecurityUtils;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 表ns_stock_in的服务层对象<br/>
 * 对应表名：ns_stock_in，表备注：入库记录表
 *
 * @author: 周建宇 at jorchi
 * @date: 2025-01-02 09:19:01
 */
@Slf4j
@Service
public class NsStockInService extends BaseService {

    @Resource
    NsStockInDao nsStockInDao;

    @Resource
    MaxIdServiceImpl maxIdService;
    @Autowired
    private NsPurchaseApplicationService nsPurchaseApplicationService;
    @Autowired
    private NsPurchaseApplicationItemService nsPurchaseApplicationItemService;
    @Autowired
    private NsPurchaseApplicationItemDao nsPurchaseApplicationItemDao;
    @Autowired
    private NsWarehouseService nsWarehouseService;
    @Autowired
    private NsInputStockService nsInputStockService;

    @Resource
    private NsPurchaseRecordService nsPurchaseRecordService;

    /**
     * 按主键查询（入库记录表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    public NsStockIn findById(Long primaryKey) {
        return nsStockInDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（入库记录表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    public NsStockIn findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsStockIn query = NsStockIn.builder().inputName(name)
                .deleted(CommonDef.DELETE_FLAG_NORMAL)
                .build();

        // 查询
        List<NsStockIn> result = nsStockInDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（入库记录表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    public List<NsStockIn> listPage(NsStockInForm nsStockInForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsStockInForm:", nsStockInForm));

        // 设置分页参数
        PageHelper.startPage(nsStockInForm.getPageNum(), nsStockInForm.getPageSize(),"create_time desc");
        return nsStockInDao.selectPageByForm(nsStockInForm);
    }

    /**
     * 删除（入库记录表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    public boolean deleteNsStockIn(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsStockInDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsStockIn nsStockIn = NsStockIn.builder()
                .stockInId(id)
                .deleted(CommonDef.DELETE_FLAG_DELETE)
                // .status(status)
                .build();

        // 更新数据
        int res = nsStockInDao.updateSelectiveByPrimaryKey(nsStockIn);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（入库记录表）<br>
     * ID 为空即为新增，否则为更新
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveNsStockIn(SavePurchaseStockIn savePurchaseStockIn) {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long purchaseId = savePurchaseStockIn.getPurchaseId();

        if (purchaseId == null) {
            throw ClientException.of("采购申请ID不能为空！");
        }

        //校验是否已经入库转换过
        NsStockInForm form = new NsStockInForm();
        form.setPageNum(1);
        form.setPageSize(1);
        form.setPurchaseId(purchaseId);
        List<NsStockIn> exists = listPage(form);
        if (!CollectionUtils.isEmpty(exists)) {
            throw ClientException.of("该单据无法重复入库");
        }
        //查询采购申请 和仓库
        NsPurchaseApplication purchase = nsPurchaseApplicationService.findById(purchaseId);
        NsWarehouse warehouse = nsWarehouseService.findById(savePurchaseStockIn.getWarehouseId());

        //查找采购记录
        NsPurchaseRecord nsPurchaseRecord = nsPurchaseRecordService.findByPurchaseId(purchaseId);

        if (nsPurchaseRecord == null) {
            throw ClientException.of("该采购单未审批结束无法入库");
        }

        //查询已有的采购项目
        List<NsPurchaseApplicationItem> nsPurchaseApplicationItems = nsPurchaseApplicationItemService.
                selectItemByPurchaseId(purchaseId);

        List<NsStockIn> nsStockIns = new ArrayList<>();
        //对每个采购明细 生成一份入库记录
        for (NsPurchaseApplicationItem nsPurchaseApplicationItem : nsPurchaseApplicationItems) {
            NsStockIn toAdd = NsStockIn.builder()
                    .regionDeptId(savePurchaseStockIn.getRegionDeptId())
                    .stockInId(maxIdService.getAndIncrement("NsStockIn"))
                    .businessType(savePurchaseStockIn.getBusinessType())
                    .purchaseId(purchaseId)
                    .purchaseCode(purchase.getPurchaseCode())
                    .supplierId(nsPurchaseApplicationItem.getSupplierId())
                    .supplier(nsPurchaseApplicationItem.getSupplierName())
                    .inputName(nsPurchaseApplicationItem.getInputName())
                    .businessId(nsPurchaseApplicationItem.getBusinessId())
                    .inputType(nsPurchaseApplicationItem.getInputType())
                    .inputCategory(nsPurchaseApplicationItem.getInputCategory())
                    .stockInDate(savePurchaseStockIn.getStockInDate())
                    .effectiveDate(nsPurchaseApplicationItem.getEffectiveDate())
                    .productionDate(nsPurchaseApplicationItem.getProductionDate())
                    .unitPrice(nsPurchaseApplicationItem.getUnitPrice())
                    .quantity(nsPurchaseApplicationItem.getQuantity())
                    .operator(loginUser.getUser().getNickName())
                    .operatorId(loginUser.getUser().getUserId())
                    .warehouseCode(warehouse.getWarehouseCode())
                    .warehouseId(savePurchaseStockIn.getWarehouseId())
                    .stockQuantity(nsPurchaseApplicationItem.getCheckQuantity())
                    .deleted(CommonDef.DELETE_FLAG_NORMAL)
                    .createBy(loginUser.getUser().getUserId())
                    .createTime(new Date())
                    .build();


            nsStockIns.add(toAdd);

        }

        nsStockInDao.saveBatch(nsStockIns);
        //更新验收信息
        List<NsPurchaseApplicationItem> items = savePurchaseStockIn.getItems();
        if (!CollectionUtils.isEmpty(items)){
            for (NsPurchaseApplicationItem item : items) {
                item.setUpdateTime(new Date());
                item.setUpdateBy(loginUser.getUser().getUserId());
                nsPurchaseApplicationItemDao.updateByPrimaryKey(item);
            }
        }
        //对入库投入品进行库存累加
        nsInputStockService.addByPurchaseItem(nsPurchaseApplicationItems, savePurchaseStockIn.getRegionDeptId());

    }

    /**
     * 重名检查(请按需使用)（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
    public void checkNameExisted(NsStockIn nsStockIn) {
    // 设置查询条件：名称，删除标志位等
    NsStockIn query = NsStockIn.builder().name(nsStockIn.getName()).build();
    List<NsStockIn> result = nsStockInDao.selectPage(query);
    if (result == null || result.isEmpty())
    return;

    // 新增或主键不同
    if (nsStockIn.getStockInId() == null || !result.get(0).getStockInId().equals(nsStockIn.getStockInId()))
    throw ClientException.of("该名称已存在！Code:400-", nsStockIn.getName(), "-k-", result.get(0).getStockInId());
    }*/

}
