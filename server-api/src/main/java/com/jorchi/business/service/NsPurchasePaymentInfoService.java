package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.NsPurchasePaymentInfoDao;
import com.jorchi.business.dao.NsSupplierDao;
import com.jorchi.business.po.NsPurchaseApplicationItem;
import com.jorchi.business.po.NsPurchasePaymentInfo;
import com.jorchi.business.form.NsPurchasePaymentInfoForm;
import com.jorchi.business.po.NsPurchaseRecord;
import com.jorchi.business.po.NsSupplier;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.DateDeserializer;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 表ns_purchase_payment_info的服务层对象<br/>
 * 对应表名：ns_purchase_payment_info，表备注：采购支付信息表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-29 12:37:40
 */
@Slf4j
@Service
public class NsPurchasePaymentInfoService extends BaseService {

    @Resource
    NsPurchasePaymentInfoDao nsPurchasePaymentInfoDao;

    @Resource
    MaxIdServiceImpl maxIdService;


    @Resource
    NsSupplierDao nsSupplierDao;
    /**
     * 按主键查询（采购支付信息表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:40
     */
    public NsPurchasePaymentInfo findById(Long primaryKey) {
        return nsPurchasePaymentInfoDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（采购支付信息表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:40
     */
    public NsPurchasePaymentInfo findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsPurchasePaymentInfo query = NsPurchasePaymentInfo.builder().supplierName(name)
            .deleted(CommonDef.DELETE_FLAG_NORMAL)
            .build();

        // 查询
        List<NsPurchasePaymentInfo> result = nsPurchasePaymentInfoDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（采购支付信息表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:40
     */
    public List<NsPurchasePaymentInfo> listPage(NsPurchasePaymentInfoForm nsPurchasePaymentInfoForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsPurchasePaymentInfoForm:", nsPurchasePaymentInfoForm));

        // 设置分页参数
        PageHelper.startPage(nsPurchasePaymentInfoForm.getPageNum(), nsPurchasePaymentInfoForm.getPageSize(),"create_time desc");
        return nsPurchasePaymentInfoDao.selectPageByForm(nsPurchasePaymentInfoForm);
    }

    /**
     * 删除（采购支付信息表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:40
     */
    public boolean deleteNsPurchasePaymentInfo(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsPurchasePaymentInfoDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsPurchasePaymentInfo nsPurchasePaymentInfo = NsPurchasePaymentInfo.builder()
            .paymentInfoId(id)
            .deleted(CommonDef.DELETE_FLAG_DELETE)
            // .status(status)
            .build();

        // 更新数据
        int res = nsPurchasePaymentInfoDao.updateSelectiveByPrimaryKey(nsPurchasePaymentInfo);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（采购支付信息表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:40
     */
    public NsPurchasePaymentInfo saveNsPurchasePaymentInfo (NsPurchasePaymentInfo nsPurchasePaymentInfo) {
        Long primaryKey = nsPurchasePaymentInfo.getPaymentInfoId();
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsPurchasePaymentInfo.setPaymentInfoId(maxIdService.getAndIncrement("NsPurchasePaymentInfo"));

            // set CreateTime
            nsPurchasePaymentInfo.setCreateTime(new Date());
            nsPurchasePaymentInfo.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            // do insert by not null properties
            nsPurchasePaymentInfoDao.insertSelective(nsPurchasePaymentInfo);
            return nsPurchasePaymentInfo;
        } else { // do update

            // set UpdateTime
            nsPurchasePaymentInfo.setUpdateTime(new Date());
            nsPurchasePaymentInfo.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            // do update by not null properties
            int count = nsPurchasePaymentInfoDao.updateSelectiveByPrimaryKey(nsPurchasePaymentInfo);
            if (count <= 0)
                nsPurchasePaymentInfoDao.insertSelective(nsPurchasePaymentInfo);
            return nsPurchasePaymentInfo;
        }
    }

    /**
     * 根据采购申请明细保存采购支付信息表
     * @param nsPurchaseApplicationItems 采购申请明细
     * @param nsPurchaseRecord 采购记录
     */
    public void saveByPurchaseItem(List<NsPurchaseApplicationItem> nsPurchaseApplicationItems, NsPurchaseRecord nsPurchaseRecord) {

        Map<Long, List<NsPurchaseApplicationItem>> supplierItemMap = nsPurchaseApplicationItems
                .stream()
                .collect(Collectors.groupingBy(NsPurchaseApplicationItem::getSupplierId));


        //查询并且填充供应商信息
        List<Long> supplierIds = new ArrayList<>(supplierItemMap
                .keySet());

        List<NsSupplier> nsSuppliers = nsSupplierDao.selectBatchByPrimaryKeys(supplierIds);

        Map<Long, NsSupplier> supplierMap = nsSuppliers.stream()
                .collect(Collectors.toMap(NsSupplier::getSupplierId, e->e, (v1, v2) -> v1));

        // 填充采购支付信息
        List<NsPurchasePaymentInfo> nsPurchasePaymentInfos = new ArrayList<>();
        supplierItemMap.forEach((supplierId, items) -> {

            NsSupplier nsSupplier = supplierMap.get(supplierId);
            if (nsSupplier == null) {
                log.warn("供应商ID:{}不存在", supplierId);
                return;
            }
            NsPurchasePaymentInfo nsPurchasePaymentInfo = NsPurchasePaymentInfo.builder()
                    .supplierId(supplierId)
                    .supplierName(nsSupplier.getSupplierName())
                    .supplierBank(nsSupplier.getBankName())
                    .supplierAccount(nsSupplier.getBankAccount())
                    .purchaseRecordId(nsPurchaseRecord.getPurchaseRecordId())
                    .paymentInfoId(maxIdService.getAndIncrement("NsPurchasePaymentInfo"))
                    .deleted(CommonDef.DELETE_FLAG_NORMAL)
                    .createTime(new Date())
                    .updateTime(new Date())
                    .paymentAmount(items.stream()
                            .map(NsPurchaseApplicationItem::getSubtotal)
                            .reduce(BigDecimal.ZERO, BigDecimal::add))
                    .build();
            nsPurchasePaymentInfos.add(nsPurchasePaymentInfo);
        });

        nsPurchasePaymentInfoDao.saveBatch(nsPurchasePaymentInfos);

    }

    /**
     * 重名检查(请按需使用)（采购支付信息表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:40
    public void checkNameExisted(NsPurchasePaymentInfo nsPurchasePaymentInfo) {
        // 设置查询条件：名称，删除标志位等
        NsPurchasePaymentInfo query = NsPurchasePaymentInfo.builder().name(nsPurchasePaymentInfo.getName()).build();
        List<NsPurchasePaymentInfo> result = nsPurchasePaymentInfoDao.selectPage(query);
        if (result == null || result.isEmpty())
            return;

        // 新增或主键不同
        if (nsPurchasePaymentInfo.getPaymentInfoId() == null || !result.get(0).getPaymentInfoId().equals(nsPurchasePaymentInfo.getPaymentInfoId()))
            throw ClientException.of("该名称已存在！Code:400-", nsPurchasePaymentInfo.getName(), "-k-", result.get(0).getPaymentInfoId());
    }*/

}
