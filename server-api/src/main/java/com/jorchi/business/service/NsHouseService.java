package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.NsHouseDao;
import com.jorchi.business.dao.NsProductionPlanDao;
import com.jorchi.business.dao.NsTaskDao;
import com.jorchi.business.form.NsHouseForm;
import com.jorchi.business.po.NsHouse;
import com.jorchi.business.vo.MapLegend;
import com.jorchi.business.vo.NsGreenhouseVo;
import com.jorchi.business.vo.NsPlantingVo;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.SecurityUtils;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.project.system.dao.SysDeptDao;
import com.jorchi.project.system.dao.SysUserDao;
import com.jorchi.project.system.dao.SysUserRoleDao;
import com.jorchi.project.system.domain.SysDept;
import com.jorchi.project.system.domain.SysUser;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 表ns_house的服务层对象<br/>
 * 对应表名：ns_house，表备注：大棚
 *
 * @author: ChenLiFeng at jorchi
 * @date: 2022-11-10 14:55:56
 */
@Slf4j
@Service
public class NsHouseService extends BaseService {

    @Resource
    NsHouseDao nsHouseDao;

    @Resource
    MaxIdServiceImpl maxIdService;

    @Resource
    SysUserDao sysUserDao;

    @Resource
    NsProductionPlanDao nsProductionPlanDao;

    @Resource
    NsTaskDao nsTaskDao;

    @Resource
    SysUserRoleDao sysUserRoleDao;

    @Resource
    SysDeptDao deptDao;


    /**
     * 按主键查询（大棚）<br>
     *
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    public NsHouse findById(Long houseId) {
        /*        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd", Locale.CHINA);*/
        NsHouse nsHouse = nsHouseDao.selectByPrimaryKey(houseId);
        if (nsHouse != null) {
            // 查询上次种植作物
            nsHouse.setLastTime(nsProductionPlanDao.selectByLast(houseId));
            Map<String, Object> map = nsProductionPlanDao.selectByThis(houseId);
            if (map != null) {
                nsHouse.setThisTime(map.get("cropName").toString());
                nsHouse.setPlantDate(map.get("harvestDate").toString());
                nsHouse.setPlantId(map.get("id").toString());
            }

            // 获取该大棚的状态值
            Long processStage = nsHouse.getProcessStage();
            // 查找该大棚的状态和颜色
            Map<String, String> houseStatus = nsHouseDao.selectHouseStatus(processStage);
            if (houseStatus != null) {
                // 颜色
                String color = houseStatus.get("color");
                // 状态
                String name = houseStatus.get("name");
                if (color != null && name != null) {
                    nsHouse.setName(name);
                    nsHouse.setColor(color);
                }
            } else {
                log.warn(CommonUtil.append("no color status found id db, houseId:", houseId, " processStage:", processStage));
            }

            // 获取大棚最新的数据采集记录
            Map<String, Object> houseData = nsHouseDao.selectRecentData(houseId);
            if (houseData != null) {
                // 环境湿度
                String q = houseData.get("q").toString();
                // 环境温度
                String t = houseData.get("t").toString();
                // 最近采集时间
                String d = houseData.get("d").toString();
                if (q != null) {
                    nsHouse.setQ(q);
                }
                if (t != null) {
                    nsHouse.setT(t);
                }
                if (d != null) {
                    nsHouse.setD(d);
                }
            }

        }
        return nsHouse;
    }

    /**
     * 按名称查询（大棚）<br>
     *
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    public NsHouse findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsHouse query = NsHouse.builder().houseName(name)
                .deleted(CommonDef.DELETE_FLAG_NORMAL)
                .build();

        // 查询
        List<NsHouse> result = nsHouseDao.selectPage(query);
        if (result == null || result.isEmpty()) {
            return null;
        }
        return result.get(0);
    }

    /**
     * 分页查询（地图大棚专用）<br>
     *
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    // @DataScope(deptAlias = "d", userAlias = "h")
    public List<NsHouse> listPage(NsHouseForm nsHouseForm) {
        if (log.isDebugEnabled()) {
            log.debug(CommonUtil.append("listPage, nsHouseForm:", nsHouseForm));
        }
        // 根据权限获取列表公共方法
        List<NsHouse> nsHouseList = getListByRole(nsHouseForm);
        // 遍历集合返显颜色
        for (NsHouse nsHouse : nsHouseList) {
            // 获取阶段id值
            Long processStage = nsHouse.getProcessStage();
            Map<String, String> houseStatus = nsHouseDao.selectHouseStatus(processStage);
            if (houseStatus != null) {
                // 颜色
                String color = houseStatus.get("color");
                // 状态
                String name = houseStatus.get("name");
                if (color != null && name != null) {
                    nsHouse.setName(name);
                    nsHouse.setColor(color);
                }
            }
        }

        return nsHouseList;
    }

    /**
     * 删除（大棚）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     *
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    public boolean deleteNsHouse(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsHouseDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsHouse nsHouse = NsHouse.builder()
                .houseId(id)
                .deleted(CommonDef.DELETE_FLAG_DELETE)
                // .status(status)
                .build();

        // 更新数据
        int res = nsHouseDao.updateSelectiveByPrimaryKey(nsHouse);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（大棚）<br>
     * ID 为空即为新增，否则为更新
     *
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    public NsHouse saveNsHouse(NsHouse nsHouse) {
        Long primaryKey = nsHouse.getHouseId();
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsHouse.setHouseId(maxIdService.getAndIncrement("NsHouse"));

            // 判断大棚编号是否重复
            // 获取大棚编号
            String houseName = nsHouse.getHouseName();
            // 检查是否存在相同的大棚编号
            Integer num = nsHouseDao.isExist(houseName, nsHouse.getRegionDeptId());
            if (num == null || num == 0) {
                // set CreateTime
                nsHouse.setCreateTime(new Date());
                nsHouse.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
                // 默认为空棚
                nsHouse.setProcessStage(CommonDef.Empty_SHED);
                // 填入农场id和name
                if (nsHouse.getRegionDeptId() == null || nsHouse.getRegionDeptId() == 0) {
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    Long deptId = loginUser.getUser().getDeptId();
                    nsHouse.setRegionDeptId(deptId);
                    SysDept sysDept = deptDao.selectDeptById(deptId);
                    nsHouse.setRegionDeptName(sysDept.getDeptName());
                }else {
                    SysDept sysDept = deptDao.selectDeptById(nsHouse.getRegionDeptId());
                    nsHouse.setRegionDeptName(sysDept.getDeptName());
                }

                // do insert by not null properties
                nsHouseDao.insertSelective(nsHouse);
                return nsHouse;
            } else {
                return null;
            }

        } else { // do update
            // 判断大棚编号是否重复
            // 获取大棚编号
            String houseName = nsHouse.getHouseName();
            // 获取大棚id
            Long houseId = nsHouse.getHouseId();
            // 检查是否存在相同的大棚编号(除了自己编号以外)
            Integer num = nsHouseDao.isExistBesidesSelf(houseName, houseId);
            // 不存在
            if (num == null || num == 0) {
                // set UpdateTime
                nsHouse.setUpdateTime(new Date());
                nsHouse.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
                // do update by not null properties
                int count = nsHouseDao.updateSelectiveByPrimaryKey(nsHouse);
                if (count <= 0)
                    nsHouseDao.insertSelective(nsHouse);
                return nsHouse;
            } else {
                return null;
            }

        }
    }

    /**
     * 获取大棚各区总数
     * 获取在植大棚数
     * 获取不在植大棚数
     */
    public NsGreenhouseVo getByRegionNameCount(String regionName) {
        NsGreenhouseVo count = new NsGreenhouseVo();
        List<NsPlantingVo> list = nsHouseDao.getByRegionNameCount(regionName);
        int noPlant = 0;
        int plant = 0;
        for (NsPlantingVo nsHouse : list) {
            if (nsHouse.getIsPlant() == 0) {
                noPlant++;
            } else {
                plant++;
            }
        }
        count.setTotalPartitions(list.size());
        count.setInPlanting(plant);
        count.setNotPlanting(noPlant);
        return count;
    }

    /**
     * 重名检查(请按需使用)（大棚）<br>
     *
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     * public void checkNameExisted(NsHouse nsHouse) {
     * // 设置查询条件：名称，删除标志位等
     * NsHouse query = NsHouse.builder().name(nsHouse.getName()).build();
     * List<NsHouse> result = nsHouseDao.selectPage(query);
     * if (result == null || result.isEmpty())
     * return;
     * <p>
     * // 新增或主键不同
     * if (nsHouse.getHouseId() == null || !result.get(0).getHouseId().equals(nsHouse.getHouseId()))
     * throw ClientException.of("该名称已存在！Code:400-", nsHouse.getName(), "-k-", result.get(0).getHouseId());
     * }
     */

    public List<SysUser> productionTechnicians(SysUser sysUser) {
        Long role = CommonDef.ROLE_ID__PRODUCTION;
        return sysUserDao.productionTechnicians(sysUser, role);
    }


    /**
     * 分页查询（大棚）<br>
     *
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    public List<NsHouse> listPageHouse(NsHouseForm nsHouseForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsHouseForm:", nsHouseForm));
        // 根据权限获取列表公共方法
        return getListByRole(nsHouseForm);
    }


    /**
     *
     */
    public void updateMapProgress() {
        // 查询所有的大棚
        List<NsHouse> houseList = nsHouseDao.selectAllHouse();
        if (houseList != null && houseList.size() != 0) {
            // 遍历集合获取大棚id
            for (NsHouse nsHouse : houseList) {
                // 筛选出育苗区的大棚(只针对正式环境)
                if (nsHouse.getHouseId() != 2634 || !nsHouse.getHouseRegionName().equals("育苗区")) {
                    Long houseId = nsHouse.getHouseId();
                    // 查看每个大棚是否还有生产计划
                    Integer flag = nsHouseDao.selectPlan(houseId);
                    // 有生产计划
                    if (flag != null && flag != 0) {

                        // 获取相对应的流程阶段
                        Long processStage = nsHouseDao.selectProcess(houseId);
                        // 如果该阶段和当前阶段不相等，则更新
                        if (processStage != null && !processStage.equals(nsHouse.getProcessStage())) {
                            nsHouseDao.updateHouseProgress(houseId, processStage);
                        }
                    } else {
                        // 无生产计划
                        // 获取当前阶段状态
                        Long processStage = nsHouse.getProcessStage();
                        // 如果不是空棚状态，设置成空棚
                        if (!processStage.equals(164L)) {
                            nsHouseDao.updateNullHouse(houseId);
                            // 设置计划完结
                            nsHouseDao.completePlan(houseId);
                        }
                    }
                }

            }

        }
    }

    /**
     * 获取大棚地图图例
     *
     * @return
     */
    public List<MapLegend> selectMapLegend() {
        return nsHouseDao.selectMapLegend();
    }

    /**
     * @param houseId 大棚Id
     */
    public void getCurrentStatus(Long houseId) {
        // 查找现在的大棚信息
        NsHouse nsHouse = nsHouseDao.selectByPrimaryKey(houseId);
        if (nsHouse != null) {
            // 获取相对应的流程阶段
            Long processStage = nsHouseDao.selectProcess(houseId);
            // 如果该阶段和当前阶段不相等，则更新
            if (processStage != null && !processStage.equals(nsHouse.getProcessStage())) {
                nsHouseDao.updateHouseProgress(houseId, processStage);
            }
        }

    }

    /**
     * 小程序根据状态筛选大棚
     */
    public List<NsHouse> selectHouseStatus(Long dictId, Long regionId) {
        return nsHouseDao.selectStatus(dictId, regionId);
    }

    /**
     * 查询没有生产任务的大棚信息
     *
     * @return
     */
    public List<NsHouse> getHouseName(NsHouseForm nsHouseVo) {
        // 根据权限获取列表公共方法
        List<NsHouse> nsHouseList = getListByRoleNoPage(nsHouseVo);

        List<NsHouse> nsHouses = new ArrayList<>();

        for (NsHouse nsHouse : nsHouseList) {
            // 筛选出育苗棚(针对正式环境)
            if (nsHouse.getHouseId() != 2634) {
                // 筛选出不是空棚的大棚
                Long processStage = nsHouse.getProcessStage();
                if (processStage != null && processStage == 164L) {
                    nsHouses.add(nsHouse);
                }
            } else {
                // 育苗棚数据
                nsHouses.add(nsHouse);
            }

        }
        return nsHouses;
    }

    /**
     * 查询所有的大棚
     *
     * @param nsHouseVo
     * @return
     */
    public List<NsHouse> getAllHouseName(NsHouseForm nsHouseVo) {
        return nsHouseDao.selectPageByForm(nsHouseVo);
    }

    /**
     * 根据权限获取大棚列表公共方法
     *
     * @param nsHouseForm
     * @return
     */
    public List<NsHouse> getListByRole(NsHouseForm nsHouseForm) {


        PageHelper.startPage(nsHouseForm.getPageNum(), nsHouseForm.getPageSize(),"create_time desc");
        List<NsHouse> nsHouseList;

        nsHouseList = nsHouseDao.selectPageByForm(nsHouseForm);

        return nsHouseList;
    }

    /**
     * 根据权限获取大棚列表公共方法(不分页)
     *
     * @param nsHouseForm
     * @return
     */
    public List<NsHouse> getListByRoleNoPage(NsHouseForm nsHouseForm) {

        List<NsHouse> nsHouseList;

        nsHouseList = nsHouseDao.selectPageByForm(nsHouseForm);

        return nsHouseList;
    }

}
