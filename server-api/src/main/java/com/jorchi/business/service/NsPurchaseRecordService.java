package com.jorchi.business.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.HistoryDataBarkDao;
import com.jorchi.business.dao.NsPurchaseApplicationItemDao;
import com.jorchi.business.dao.NsPurchasePaymentInfoDao;
import com.jorchi.business.dao.NsPurchaseRecordDao;
import com.jorchi.business.form.NsPurchaseApplicationItemForm;
import com.jorchi.business.form.QueryPurchaseForm;
import com.jorchi.business.form.SavePurchasePaymentForm;
import com.jorchi.business.po.NsPurchaseApplicationItem;
import com.jorchi.business.po.NsPurchasePaymentInfo;
import com.jorchi.business.po.NsPurchaseRecord;
import com.jorchi.business.form.NsPurchaseRecordForm;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.DateDeserializer;
import com.jorchi.common.utils.SecurityUtils;
import com.jorchi.common.utils.StringUtils;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;


/**
 * 表ns_purchase_record的服务层对象<br/>
 * 对应表名：ns_purchase_record，表备注：采购记录表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-29 12:37:04
 */
@Slf4j
@Service
public class NsPurchaseRecordService extends BaseService {

    @Resource
    NsPurchaseRecordDao nsPurchaseRecordDao;

    @Resource
    MaxIdServiceImpl maxIdService;

    @Resource
    NsPurchasePaymentInfoDao nsPurchasePaymentInfoDao;

    @Resource
    private NsPurchaseApplicationItemDao nsPurchaseApplicationItemDao;

    /**
     * 按主键查询（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    public NsPurchaseRecord findById(Long primaryKey) {
        return nsPurchaseRecordDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    public NsPurchaseRecord findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsPurchaseRecord query = NsPurchaseRecord.builder()
            .deleted(CommonDef.DELETE_FLAG_NORMAL)
            .build();

        // 查询
        List<NsPurchaseRecord> result = nsPurchaseRecordDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    public List<NsPurchaseRecord> listPage(NsPurchaseRecordForm nsPurchaseRecordForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsPurchaseRecordForm:", nsPurchaseRecordForm));

        // 设置分页参数
        PageHelper.startPage(nsPurchaseRecordForm.getPageNum(), nsPurchaseRecordForm.getPageSize(),"create_time desc");
        return nsPurchaseRecordDao.selectPageByForm(nsPurchaseRecordForm);
    }

    /**
     * 删除（采购记录表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    public boolean deleteNsPurchaseRecord(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsPurchaseRecordDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsPurchaseRecord nsPurchaseRecord = NsPurchaseRecord.builder()
            .purchaseRecordId(id)
            .deleted(CommonDef.DELETE_FLAG_DELETE)
            // .status(status)
            .build();

        // 更新数据
        int res = nsPurchaseRecordDao.updateSelectiveByPrimaryKey(nsPurchaseRecord);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（采购记录表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    public NsPurchaseRecord saveNsPurchaseRecord (NsPurchaseRecord nsPurchaseRecord) {
        Long primaryKey = nsPurchaseRecord.getPurchaseRecordId();
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsPurchaseRecord.setPurchaseRecordId(maxIdService.getAndIncrement("NsPurchaseRecord"));

            // set CreateTime
            nsPurchaseRecord.setCreateTime(new Date());
            nsPurchaseRecord.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            // do insert by not null properties
            nsPurchaseRecordDao.insertSelective(nsPurchaseRecord);
            return nsPurchaseRecord;
        } else { // do update

            // set UpdateTime
            nsPurchaseRecord.setUpdateTime(new Date());
            nsPurchaseRecord.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            // do update by not null properties
            int count = nsPurchaseRecordDao.updateSelectiveByPrimaryKey(nsPurchaseRecord);
            if (count <= 0)
                nsPurchaseRecordDao.insertSelective(nsPurchaseRecord);
            return nsPurchaseRecord;
        }
    }

    /**
     * 保存采购付款信息
     * @param form
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePurchasePayment(SavePurchasePaymentForm form) {

        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long purchaseRecordId = form.getPurchaseRecordId();

        if (purchaseRecordId == null)
            throw ClientException.of("采购记录ID不能为空！");

        NsPurchaseRecord nsPurchaseRecord = nsPurchaseRecordDao.selectByPrimaryKey(purchaseRecordId);

        if (nsPurchaseRecord == null)
            throw ClientException.of("采购记录不存在！");

        if (nsPurchaseRecord.getPaymentStatus() != null && nsPurchaseRecord.getPaymentStatus() == 1)
            throw ClientException.of("该采购记录已支付，请勿重复操作！");

        List<NsPurchasePaymentInfo> paymentList = form.getPaymentList();


        for (NsPurchasePaymentInfo nsPurchasePaymentInfo : paymentList) {
            nsPurchasePaymentInfo.setUpdateTime(new Date());
            nsPurchasePaymentInfo.setUpdateBy(loginUser.getUser().getUserId());
            nsPurchasePaymentInfoDao.updateByPrimaryKey(nsPurchasePaymentInfo);
        }

        //更新验收信息
        List<NsPurchaseApplicationItem> items = form.getItems();
        if (!CollectionUtils.isEmpty(items)){
            for (NsPurchaseApplicationItem item : items) {
                item.setUpdateTime(new Date());
                item.setUpdateBy(loginUser.getUser().getUserId());
                nsPurchaseApplicationItemDao.updateByPrimaryKey(item);
            }
        }

        //全部附件上传完成
        if (paymentList
                .stream().allMatch(e-> StringUtils.isNotEmpty(e.getAttachment()))){
            //改成已支付
            nsPurchaseRecord.setPaymentStatus(1);
            nsPurchaseRecord.setPaymentDate(new Date());
            nsPurchaseRecord.setPaymentAmount(paymentList
                    .stream()
                    .map(NsPurchasePaymentInfo::getPaymentAmount)
                    .reduce(java.math.BigDecimal.ZERO, BigDecimal::add));

            nsPurchaseRecordDao.updateByPrimaryKey(nsPurchaseRecord);

        }
    }

    /**
     * 根据供应商查询采购单
     * @param queryPurchaseForm 查询条件
     * @return
     */
    public Page<NsPurchaseRecord> queryPurchaseBySupplier(QueryPurchaseForm queryPurchaseForm) {

        PageHelper.startPage(queryPurchaseForm.getPageNum(), queryPurchaseForm.getPageSize(),"create_time desc");
        Page<NsPurchaseRecord> nsPurchaseRecords = nsPurchaseRecordDao.selectPurchaseBySupplier(queryPurchaseForm);
        return nsPurchaseRecords;
    }

    /**
     * 根据采购ID查找采购记录
     * @param purchaseId
     * @return
     */
    public NsPurchaseRecord findByPurchaseId(Long purchaseId) {
        NsPurchaseRecordForm nsPurchaseRecordForm = new NsPurchaseRecordForm();
        nsPurchaseRecordForm.setPurchaseId(purchaseId);
        nsPurchaseRecordForm.setPageNum(1);
        nsPurchaseRecordForm.setPageSize(1);
        List<NsPurchaseRecord> nsPurchaseRecords = listPage(nsPurchaseRecordForm);

        if (CollectionUtils.isEmpty(nsPurchaseRecords)){
            return null;
        }
        return nsPurchaseRecords.get(0);

    }

    /**
     * 重名检查(请按需使用)（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
    public void checkNameExisted(NsPurchaseRecord nsPurchaseRecord) {
        // 设置查询条件：名称，删除标志位等
        NsPurchaseRecord query = NsPurchaseRecord.builder().name(nsPurchaseRecord.getName()).build();
        List<NsPurchaseRecord> result = nsPurchaseRecordDao.selectPage(query);
        if (result == null || result.isEmpty())
            return;

        // 新增或主键不同
        if (nsPurchaseRecord.getPurchaseRecordId() == null || !result.get(0).getPurchaseRecordId().equals(nsPurchaseRecord.getPurchaseRecordId()))
            throw ClientException.of("该名称已存在！Code:400-", nsPurchaseRecord.getName(), "-k-", result.get(0).getPurchaseRecordId());
    }*/

}
