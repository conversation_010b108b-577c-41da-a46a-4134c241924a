package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.NsMyKnowledgeBaseDao;
import com.jorchi.business.po.NsMyKnowledgeBase;
import com.jorchi.business.form.NsMyKnowledgeBaseForm;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.DateDeserializer;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.*;


/**
 * 表ns_my_knowledge_base的服务层对象<br/>
 * 对应表名：ns_my_knowledge_base，表备注：我的知识库
 * @author: ysj at jorchi
 * @date: 2022-11-17 16:32:13
 */
@Slf4j
@Service
public class NsMyKnowledgeBaseService extends BaseService {

    @Resource
    NsMyKnowledgeBaseDao nsMyKnowledgeBaseDao;

    @Resource
    MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    public NsMyKnowledgeBase findById(Long primaryKey) {
        return nsMyKnowledgeBaseDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    public NsMyKnowledgeBase findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsMyKnowledgeBase query = NsMyKnowledgeBase.builder().cropName(name)
            .deleted(CommonDef.DELETE_FLAG_NORMAL)
            .build();

        // 查询
        List<NsMyKnowledgeBase> result = nsMyKnowledgeBaseDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    public List<NsMyKnowledgeBase> listPage(NsMyKnowledgeBaseForm nsMyKnowledgeBaseForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsMyKnowledgeBaseForm:", nsMyKnowledgeBaseForm));

        // 设置分页参数
        PageHelper.startPage(nsMyKnowledgeBaseForm.getPageNum(), nsMyKnowledgeBaseForm.getPageSize(),"create_time desc");
        return nsMyKnowledgeBaseDao.selectPageByForm(nsMyKnowledgeBaseForm);
    }

    /**
     * 删除（我的知识库）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    public boolean deleteNsMyKnowledgeBase(Long id,Long userId) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsMyKnowledgeBaseDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsMyKnowledgeBase nsMyKnowledgeBase = NsMyKnowledgeBase.builder()
            .knowledgeId(id)
            .updateBy(userId)
            .updateTime(new Date())
            .deleted(CommonDef.DELETE_FLAG_DELETE)
            // .status(status)
            .build();

        // 更新数据
        int res = nsMyKnowledgeBaseDao.updateSelectiveByPrimaryKey(nsMyKnowledgeBase);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（我的知识库）<br>
     * ID 为空即为新增，否则为更新
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    public NsMyKnowledgeBase saveNsMyKnowledgeBase (NsMyKnowledgeBase nsMyKnowledgeBase,Long userId) {
        Date date = new Date();
        Long primaryKey = nsMyKnowledgeBase.getKnowledgeId();
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsMyKnowledgeBase.setKnowledgeId(maxIdService.getAndIncrement("NsMyKnowledgeBase"));

            // set CreateTime
            nsMyKnowledgeBase.setCreateBy(userId);
            nsMyKnowledgeBase.setCreateTime(date);
            nsMyKnowledgeBase.setUpdateBy(userId);
            nsMyKnowledgeBase.setUpdateTime(date);
            nsMyKnowledgeBase.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            // do insert by not null properties
            nsMyKnowledgeBaseDao.insertSelective(nsMyKnowledgeBase);
            return nsMyKnowledgeBase;
        } else { // do update

            // set UpdateTime
            nsMyKnowledgeBase.setUpdateBy(userId);
            nsMyKnowledgeBase.setUpdateTime(new Date());
            nsMyKnowledgeBase.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            // do update by not null properties
            int count = nsMyKnowledgeBaseDao.updateSelectiveByPrimaryKey(nsMyKnowledgeBase);
            if (count <= 0)
                nsMyKnowledgeBaseDao.insertSelective(nsMyKnowledgeBase);
            return nsMyKnowledgeBase;
        }
    }

    /**
     * 重名检查(请按需使用)（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
    public void checkNameExisted(NsMyKnowledgeBase nsMyKnowledgeBase) {
        // 设置查询条件：名称，删除标志位等
        NsMyKnowledgeBase query = NsMyKnowledgeBase.builder().name(nsMyKnowledgeBase.getName()).build();
        List<NsMyKnowledgeBase> result = nsMyKnowledgeBaseDao.selectPage(query);
        if (result == null || result.isEmpty())
            return;

        // 新增或主键不同
        if (nsMyKnowledgeBase.getKnowledgeId() == null || !result.get(0).getKnowledgeId().equals(nsMyKnowledgeBase.getKnowledgeId()))
            throw ClientException.of("该名称已存在！Code:400-", nsMyKnowledgeBase.getName(), "-k-", result.get(0).getKnowledgeId());
    }*/

}
