package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.*;
import com.jorchi.business.dto.InputTypeAndIdDto;
import com.jorchi.business.form.NsInputStockForm;
import com.jorchi.business.po.*;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 表ns_input_stock的服务层对象<br/>
 * 对应表名：ns_input_stock，表备注：投入品库存表
 *
 * @author: 周建宇 at jorchi
 * @date: 2024-12-13 11:01:03
 */
@Slf4j
@Service
public class NsInputStockService extends BaseService {

    @Resource
    NsInputStockDao nsInputStockDao;

    @Resource
    MaxIdServiceImpl maxIdService;

    @Resource
    NsCropBreedsDao nsCropBreedsDao;

    @Resource
    NsFertilizerDao nsFertilizerDao;

    @Resource
    NsAgriculturalMaterialsDao agriculturalMaterialsDao;

    @Resource
    NsFarmFilmDao nsFarmFilmDao;

    @Resource
    NsInputOtherDao nsInputOtherDao;

    /**
     * 按主键查询（投入品库存表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    public NsInputStock findById(Long primaryKey) {
        return nsInputStockDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（投入品库存表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    public NsInputStock findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsInputStock query = NsInputStock.builder().inputName(name)
                .deleted(CommonDef.DELETE_FLAG_NORMAL)
                .build();

        // 查询
        List<NsInputStock> result = nsInputStockDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（投入品库存表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    public List<NsInputStock> listPage(NsInputStockForm nsInputStockForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsInputStockForm:", nsInputStockForm));

        // 设置分页参数
        PageHelper.startPage(nsInputStockForm.getPageNum(), nsInputStockForm.getPageSize(), "create_time desc");
        return nsInputStockDao.selectPageByForm(nsInputStockForm);
    }

    /**
     * 删除（投入品库存表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    public boolean deleteNsInputStock(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsInputStockDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsInputStock nsInputStock = NsInputStock.builder()
                .inputId(id)
                .deleted(CommonDef.DELETE_FLAG_DELETE)
                // .status(status)
                .build();

        // 更新数据
        int res = nsInputStockDao.updateSelectiveByPrimaryKey(nsInputStock);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（投入品库存表）<br>
     * ID 为空即为新增，否则为更新
     *
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    public NsInputStock saveNsInputStock(NsInputStock nsInputStock) {
        Long primaryKey = nsInputStock.getInputId();
        //校验库存预警
        if (nsInputStock.getStockWarningLine() != null
                && nsInputStock.getStockQuantity().compareTo(nsInputStock.getStockWarningLine()) < 0) {
            nsInputStock.setWarningStatus("预警");
        } else {
            nsInputStock.setWarningStatus("正常");
        }

        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsInputStock.setInputId(maxIdService.getAndIncrement("NsInputStock"));

            // set CreateTime
            nsInputStock.setCreateTime(new Date());
            nsInputStock.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            if (nsInputStock.getStockQuantity() == null) {
                nsInputStock.setStockQuantity(BigDecimal.ZERO);
            }

            // do insert by not null properties
            nsInputStockDao.insertSelective(nsInputStock);
            return nsInputStock;
        } else { // do update

            // set UpdateTime
            nsInputStock.setUpdateTime(new Date());
            nsInputStock.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            // do update by not null properties
            int count = nsInputStockDao.updateSelectiveByPrimaryKey(nsInputStock);
            if (count <= 0)
                nsInputStockDao.insertSelective(nsInputStock);
            return nsInputStock;
        }
    }

    /**
     * 根据投入品ID批量查询库存
     *
     * @param inputTypeAndIdDtos 投入品类型和ID列表
     * @return
     */
    public List<NsInputStock> selectByBusinessIds(List<InputTypeAndIdDto> inputTypeAndIdDtos) {
        return nsInputStockDao.selectByBusinessIds(inputTypeAndIdDtos);
    }

    /**
     * 根据盘点投入品获取投入品类型和对应ID列表
     *
     * @param items 采购详情
     * @return
     */
    private List<InputTypeAndIdDto> getInputTypeAndIdDtos(List<NsPurchaseApplicationItem> items) {
        List<InputTypeAndIdDto> inputTypeAndIdDtos = new ArrayList<>();
        items
                .stream()
                .collect(Collectors.groupingBy(NsPurchaseApplicationItem::getInputType))
                .forEach((k, v) -> {
                    inputTypeAndIdDtos.add(InputTypeAndIdDto.builder()
                            .type(k)
                            .businessIds(v.stream().map(NsPurchaseApplicationItem::getBusinessId).collect(Collectors.toList()))
                            .build());
                });
        return inputTypeAndIdDtos;
    }

    /**
     * 根据采购进行入库
     *
     * @param items 采购详情
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
    public void addByPurchaseItem(List<NsPurchaseApplicationItem> items, Long regionDeptId) {

        //加锁查询已有库存
        List<NsInputStock> nsInputStocks = nsInputStockDao.selectByBusinessIdsWithLock(getInputTypeAndIdDtos(items));

        Map<String, List<NsPurchaseApplicationItem>> purchaseItemMap = items
                .stream()
                .collect(Collectors.groupingBy(e -> e.getInputType() + "_"
                        + e.getBusinessId() + "_" + e.getUnit()));

        //找出一有的库存进行更新，更新表示  inputType_businessId_unit
        nsInputStocks.forEach(nsInputStock -> {
            List<NsPurchaseApplicationItem> purchaseItems =
                    purchaseItemMap.get(nsInputStock.getInputType()
                            + "_" + nsInputStock.getBusinessId() + "_" + nsInputStock.getSpec());

            if (purchaseItems == null || purchaseItems.isEmpty())
                return;

            //库存累加
            nsInputStock
                    .setStockQuantity(nsInputStock.getStockQuantity()
                            .add(purchaseItems
                                    .stream()
                                    .map(NsPurchaseApplicationItem::getCheckQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)));

            if (nsInputStock.getStockQuantity().compareTo(BigDecimal.ZERO) < 0) {
                throw ClientException.of("库存不足，无法继续操作");
            }
            //校验库存预警
            if (nsInputStock.getStockWarningLine() != null
                    && nsInputStock.getStockQuantity().compareTo(nsInputStock.getStockWarningLine()) < 0) {
                nsInputStock.setWarningStatus("预警");
            } else {
                nsInputStock.setWarningStatus("正常");
            }

            nsInputStockDao.updateByPrimaryKey(nsInputStock);
        });
        //
        List<String> existBusinessIds = nsInputStocks
                .stream()
                .map(e -> e.getInputType() + "_" + e.getBusinessId()+"_"+e.getSpec())
                .collect(Collectors.toList());
        //找到不存在的投入品进行新增
        List<NsInputStock> toAddStock = purchaseItemMap.keySet()
                .stream()
                .filter(businessId -> !existBusinessIds.contains(businessId))
                .map(businessId -> {
                    String[] split = businessId.split("_");
                    NsInputStock nsInputStock = NsInputStock.builder()
                            .businessId(Long.valueOf(split[1]))
                            .inputType(Integer.valueOf(split[0]))
                            .stockQuantity(purchaseItemMap.get(businessId)
                                    .stream()
                                    .map(NsPurchaseApplicationItem::getCheckQuantity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                            ).regionDeptId(regionDeptId)
                            .build();
                    return nsInputStock;
                })
                .collect(Collectors.toList());

        //填充其他信息;
        if (!CollectionUtils.isEmpty(toAddStock)) {
            for (NsInputStock nsInputStock : toAddStock) {
                nsInputStock.setInputId(maxIdService.getAndIncrement("NsInputStock"));

                switch (nsInputStock.getInputType()) {
                    case 2:

                        nsInputStock.setInputCategory("肥料");
                        NsFertilizer nsFertilizer = nsFertilizerDao.selectByPrimaryKey(nsInputStock.getBusinessId());
                        if (nsFertilizer != null) {

                            nsInputStock.setInputName(nsFertilizer.getDetails());
                            nsInputStock.setInputSubCategory(nsFertilizer.getFertilizerType());
//                        nsInputStock.setInputSubCategory(nsFertilizer.getFertilizerSubCategory());
                            nsInputStock.setStockWarningLine(nsFertilizer.getStockWarningLine());
                        }
                        break;
                    case 3:
                        nsInputStock.setInputCategory("植保剂");
                        NsAgriculturalMaterials agriculturalMaterials = agriculturalMaterialsDao.selectByPrimaryKey(nsInputStock.getBusinessId());
                        if (agriculturalMaterials != null) {

                            nsInputStock.setInputName(agriculturalMaterials.getBrandName());
                            nsInputStock.setInputSubCategory(agriculturalMaterials.getAgriculturalMaterialsType());
//                        nsInputStock.setInputSubCategory(agriculturalMaterials.getMaterialSubCategory());
                            nsInputStock.setStockWarningLine(agriculturalMaterials.getStockWarningLine());
                        }
                        break;
                    case 4:
                        nsInputStock.setInputCategory("种子");
                        NsCropBreeds nsCropBreeds = nsCropBreedsDao.selectByPrimaryKey(nsInputStock.getBusinessId());
                        if (nsCropBreeds != null) {

                            nsInputStock.setInputName(nsCropBreeds.getCropName());
                            nsInputStock.setInputSubCategory(nsCropBreeds.getBreeds());
//                        nsInputStock.setInputSubCategory(nsCropBreeds.getCropSubCategory());
                            nsInputStock.setStockWarningLine(nsCropBreeds.getStockWarningLine());
                        }
                        break;
                    case 5:
                        nsInputStock.setInputCategory("农膜");
                        NsFarmFilm nsFarmFilm = nsFarmFilmDao.selectByPrimaryKey(nsInputStock.getBusinessId());

                        if (nsFarmFilm != null) {

                            nsInputStock.setInputName(nsFarmFilm.getDetails());
                            nsInputStock.setInputSubCategory(nsFarmFilm.getFilmType());
//                        nsInputStock.setInputSubCategory(nsFarmFilm.getFilmSubCategory());
                            nsInputStock.setStockWarningLine(nsFarmFilm.getStockWarningLine());
                        }
                        break;
                    case 6:
                        nsInputStock.setInputCategory("其他");
                        NsInputOther nsInputOther = nsInputOtherDao.selectByPrimaryKey(nsInputStock.getBusinessId());

                        if (nsInputOther != null) {

                            nsInputStock.setInputName(nsInputOther.getDetails());
                            nsInputStock.setInputSubCategory(nsInputOther.getInputType());
//                        nsInputStock.setInputSubCategory(nsInputOther.getOtherSubCategory());
                            nsInputStock.setStockWarningLine(nsInputOther.getStockWarningLine());
                        }
                        break;
                    default:
                        break;
                }
                nsInputStock.setCreateTime(new Date());
                nsInputStock.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
                if (nsInputStock.getStockWarningLine() != null
                        && nsInputStock.getStockQuantity().compareTo(nsInputStock.getStockWarningLine()) < 0) {
                    nsInputStock.setWarningStatus("预警");
                } else {
                    nsInputStock.setWarningStatus("正常");
                }

            }
            //批量投入品入库
            nsInputStockDao.saveBatch(toAddStock);


        }
    }

    /**
     * 重名检查(请按需使用)（投入品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
    public void checkNameExisted(NsInputStock nsInputStock) {
    // 设置查询条件：名称，删除标志位等
    NsInputStock query = NsInputStock.builder().name(nsInputStock.getName()).build();
    List<NsInputStock> result = nsInputStockDao.selectPage(query);
    if (result == null || result.isEmpty())
    return;

    // 新增或主键不同
    if (nsInputStock.getInputId() == null || !result.get(0).getInputId().equals(nsInputStock.getInputId()))
    throw ClientException.of("该名称已存在！Code:400-", nsInputStock.getName(), "-k-", result.get(0).getInputId());
    }*/

}
