package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.NsProcessTaskDao;
import com.jorchi.business.po.NsProcessTask;
import com.jorchi.business.form.NsProcessTaskForm;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.DateDeserializer;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.*;


/**
 * 表ns_process_task的服务层对象<br/>
 * 对应表名：ns_process_task，表备注：流程任务表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-25 15:17:09
 */
@Slf4j
@Service
public class NsProcessTaskService extends BaseService {

    @Resource
    NsProcessTaskDao nsProcessTaskDao;

    @Resource
    MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    public NsProcessTask findById(Long primaryKey) {
        return nsProcessTaskDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    public NsProcessTask findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsProcessTask query = NsProcessTask.builder()
            .deleted(CommonDef.DELETE_FLAG_NORMAL)
            .build();

        // 查询
        List<NsProcessTask> result = nsProcessTaskDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    public List<NsProcessTask> listPage(NsProcessTaskForm nsProcessTaskForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsProcessTaskForm:", nsProcessTaskForm));

        // 设置分页参数
        PageHelper.startPage(nsProcessTaskForm.getPageNum(), nsProcessTaskForm.getPageSize(),"create_time desc");
        return nsProcessTaskDao.selectPageByForm(nsProcessTaskForm);
    }

    /**
     * 删除（流程任务表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    public boolean deleteNsProcessTask(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsProcessTaskDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsProcessTask nsProcessTask = NsProcessTask.builder()
            .taskId(id)
            .deleted(CommonDef.DELETE_FLAG_DELETE)
            // .status(status)
            .build();

        // 更新数据
        int res = nsProcessTaskDao.updateSelectiveByPrimaryKey(nsProcessTask);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（流程任务表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    public NsProcessTask saveNsProcessTask (NsProcessTask nsProcessTask) {
        Long primaryKey = nsProcessTask.getTaskId();
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsProcessTask.setTaskId(maxIdService.getAndIncrement("NsProcessTask"));

            // set CreateTime
            nsProcessTask.setCreateTime(new Date());
            nsProcessTask.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            // do insert by not null properties
            nsProcessTaskDao.insertSelective(nsProcessTask);
            return nsProcessTask;
        } else { // do update

            // set UpdateTime
            nsProcessTask.setUpdateTime(new Date());
            nsProcessTask.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            // do update by not null properties
            int count = nsProcessTaskDao.updateSelectiveByPrimaryKey(nsProcessTask);
            if (count <= 0)
                nsProcessTaskDao.insertSelective(nsProcessTask);
            return nsProcessTask;
        }
    }

    /**
     * 重名检查(请按需使用)（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
    public void checkNameExisted(NsProcessTask nsProcessTask) {
        // 设置查询条件：名称，删除标志位等
        NsProcessTask query = NsProcessTask.builder().name(nsProcessTask.getName()).build();
        List<NsProcessTask> result = nsProcessTaskDao.selectPage(query);
        if (result == null || result.isEmpty())
            return;

        // 新增或主键不同
        if (nsProcessTask.getTaskId() == null || !result.get(0).getTaskId().equals(nsProcessTask.getTaskId()))
            throw ClientException.of("该名称已存在！Code:400-", nsProcessTask.getName(), "-k-", result.get(0).getTaskId());
    }*/

}
