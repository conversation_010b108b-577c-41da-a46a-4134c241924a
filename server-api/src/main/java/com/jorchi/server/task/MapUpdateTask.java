package com.jorchi.server.task;

import com.jorchi.business.service.NsHouseService;
import com.jorchi.server.po.TMessageConsumer;
import com.jorchi.server.service.MessageConsumerService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import pdfc.claim.common.ClientException;

/**
 * @Author: xubinbin
 * @Date: 2023/1/30 13:42
 * @Describe: 每日凌晨一点更新地图大棚的色块
 */
@Component
public class MapUpdateTask {
    private static final Log log = LogFactory.getLog(MapUpdateTask.class);

    @Autowired
    MessageConsumerService messageConsumerService;

    @Autowired
    NsHouseService nsHouseService;

    @Scheduled(cron = "0 0 1 * * ?")
//    @Scheduled(cron = "0 14 16 * * ?")
    public void doMapTask(){
        log.info("MapUpdateTask.doMapTask start! ");

        TMessageConsumer locked = null;
        try {
            // 使用分布式事物锁
            locked = messageConsumerService.tryLock(MessageConsumerService.LOCK_TYPE.MAP_UPDATE_TASK);
            // 上一次处理还未结束
            if (locked != null && locked.isMyNodeRunning()) {
                locked = null;
                throw ClientException.of("MyNodeIsRunning?");
            }
            if (locked == null) {
                log.info("i lock failed！");
                return;
            }

            // 处理业务开始，每个业务都应该自己 try catch 住，以免影响其它业务
            updateMapProgress();

        }catch (ClientException e) {
            throw e;
        }
        finally {
            if (locked != null)
                messageConsumerService.unLock(locked, true);
        }
        log.info("MapUpdateTask.doMapTask start ok.");
    }

    private void updateMapProgress() {
        try{
            log.info("updateMaoProgress Start!");
            nsHouseService.updateMapProgress();
        }catch (Throwable e){
            log.error("updateMapProgress error !",e);
        }
    }

}
