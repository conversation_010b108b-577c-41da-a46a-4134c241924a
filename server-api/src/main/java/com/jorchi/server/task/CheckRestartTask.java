package com.jorchi.server.task;

import com.jorchi.business.service.NsSysEquipmentService;
import com.jorchi.framework.config.SystemPropertyConfig;
import com.jorchi.server.po.TMessageConsumer;
import com.jorchi.server.service.MessageConsumerService;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import pdfc.claim.common.ClientException;

/**
 * @Author: xubinbin
 * @Date: 2023/4/18 9:12
 * @Describe: 每一小时检查离线率，全部离线将重启mqtt服务
 */
@Component
public class CheckRestartTask  {
    private static final Log log = LogFactory.getLog(CheckRestartTask.class);

    @Autowired
    MessageConsumerService messageConsumerService;

    @Autowired
    NsSysEquipmentService nsSysEquipmentService;

    @Scheduled(cron = "0 0 */1 * * ?")
    public void doCheckRestartTask(){
        log.info("CheckRestartTask.doCheckRestartTask start.");

        TMessageConsumer locked = null;

        try {
            // 非生产环境不用检测
            if (!SystemPropertyConfig.isProduction()) {
                log.info("checkRestartByOfflineRate exit for not production...");
                return;
            }

            // 使用分布式事物锁
            locked = messageConsumerService.tryLock(MessageConsumerService.LOCK_TYPE.CHECK_RESTART_TASK);
            // 上一次处理还未结束
            if (locked != null && locked.isMyNodeRunning()) {
                locked = null;
                throw ClientException.of("MyNodeIsRunning?");
            }
            if (locked == null) {
                log.info("i lock failed！");
                return;
            }

            // 处理业务开始，每个业务都应该自己 try catch 住，以免影响其它业务
            checkRestart();

        }catch (ClientException e) {
            throw e;
        }
        finally {
            if (locked != null) {
                messageConsumerService.unLock(locked, true);
            }
        }
        log.info("CheckRestartTask.doCheckRestartTask ok.");
    }

    private void checkRestart() {
        try{
            log.info("CheckRestart start!!");
            nsSysEquipmentService.checkRestart();
        } catch (Throwable e) {
            log.error("CheckRestart error!", e);
        }
    }


}
