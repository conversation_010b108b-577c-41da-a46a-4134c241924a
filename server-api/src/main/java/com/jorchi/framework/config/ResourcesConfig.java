package com.jorchi.framework.config;

import com.jorchi.common.constant.Constants;
import com.jorchi.framework.interceptor.ClientVersionHandlerInterceptor;
import com.jorchi.framework.interceptor.DangerActionHandlerInterceptor;
import com.jorchi.framework.interceptor.DeptIdValidateInterceptor;
import com.jorchi.framework.interceptor.RepeatSubmitInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 通用配置
 *
 * <AUTHOR>
 */
@Configuration
public class ResourcesConfig implements WebMvcConfigurer
{
    @Autowired
    private RepeatSubmitInterceptor repeatSubmitInterceptor;


    @Autowired
    private ClientVersionHandlerInterceptor clientVersionHandlerInterceptor;

    @Autowired
    private DangerActionHandlerInterceptor dangerActionHandlerInterceptor;

    @Autowired
    private DeptIdValidateInterceptor deptIdValidateInterceptor;

    @Value("${document-fileUrl:/home/<USER>/}")
    public String fileUrl;
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry)
    {
        /** 本地文件上传路径 */
        registry.addResourceHandler(Constants.RESOURCE_PREFIX + "/**").addResourceLocations("file:" + fileUrl);

        //** swagger配置 */
        // registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        // registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    /**
     * 自定义拦截规则
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry)
    {
//        registry.addInterceptor(dangerActionHandlerInterceptor).addPathPatterns("/**");
        registry.addInterceptor(repeatSubmitInterceptor).addPathPatterns("/**");
        registry.addInterceptor(clientVersionHandlerInterceptor).addPathPatterns("/**");
        registry.addInterceptor(deptIdValidateInterceptor).addPathPatterns("/**")
                .excludePathPatterns("/login","/login/appLogin","/system/dept/listAll");
    }

    /**
     * 跨域配置
     */
    @Bean
    public CorsFilter corsFilter()
    {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置访问源地址
        //config.addAllowedOrigin("*");
        config.addAllowedOriginPattern("*");
        // 设置访问源请求头
        config.addAllowedHeader("*");
        // 设置访问源请求方法
        config.addAllowedMethod("*");
        // 对接口配置跨域设置
        source.registerCorsConfiguration("/**", config);
        return new CorsFilter(source);
    }
}
