package com.jorchi.framework.config;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AliyunSmsConfig {

    @Value("${system.aliyun-sms.access-key-id}")
    public String accessKeyId;
    @Value("${system.aliyun-sms.access-key-secret}")
    public String accessKeySecret;
    @Value("${system.aliyun-sms.endpoint}")
    public String endpoint;

    @Bean(name = "aliyunSmsClient")
    public Client aliyunSmsClient() throws Exception{
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 您的AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = endpoint;
        return new Client(config);
    }
}
