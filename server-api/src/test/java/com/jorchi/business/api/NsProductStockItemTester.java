package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsProductStockItem;
import com.jorchi.business.service.NsProductStockItemService;
import com.jorchi.business.form.NsProductStockItemForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_product_stock_item-模块测试<br/>
 * 对应表名：ns_product_stock_item，表备注：农产品库存项目表
 * @author: 周建宇 at jorchi
 * @date: 2025-06-14 16:04:47
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProductStockItemTester extends BaseApi {

    @Resource
    NsProductStockItemService nsProductStockItemService;

    @Resource
    NsProductStockItemApi nsProductStockItemApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @Test
    public void findByIdNsProductStockItemTest() {
        Long itemId;
        itemId = 1L;

        NsProductStockItem bean = nsProductStockItemService.findById(itemId);
        if (bean == null)
            throw ClientException.of("NsProductStockItem not found error!");

        log.info("findById NsProductStockItemTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @Test
    public void listPageNsProductStockItemTest() {
        NsProductStockItemForm nsProductStockItemVo = new NsProductStockItemForm();
        nsProductStockItemVo.setPageNum(1);
        nsProductStockItemVo.setPageSize(10);
        log.info("listPage NsProductStockItemTest result:");
        log.info(getDataTable(nsProductStockItemService.listPage(nsProductStockItemVo)).toString());
    }

    /**
     * 删除测试（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @Test
    public void deleteNsProductStockItemTest() {
        Long id = 0L;
        log.info("delete NsProductStockItemTest result:");
        log.info(AjaxResult.success(nsProductStockItemService.deleteNsProductStockItem(id)).toString());
    }

    /**
     * 新增或保存测试（农产品库存项目表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @Test
    public void saveNsProductStockItemTest () {
        NsProductStockItem nsProductStockItem = new NsProductStockItem();
        // 农产品库存项目id
        // nsProductStockItem.setItemId(0L);
        // 农产品类别
        nsProductStockItem.setCropType("农产品类别");
        // 科别
        nsProductStockItem.setCropCategory("科别");
        // 品种
        nsProductStockItem.setBreeds("品种");
        // 农产品名称
        nsProductStockItem.setCropName("农产品名称");
        // 规格
        nsProductStockItem.setSpec("规格");
        // 数量
        nsProductStockItem.setNum(0);
        // 总重量
        nsProductStockItem.setWeight(new BigDecimal(0));
        // 删除标记
        nsProductStockItem.setDeleted(0);
        // 创建人
        nsProductStockItem.setCreateBy(0L);
        // 创建时间
        nsProductStockItem.setCreateTime(new Date());
        // 更新人
        nsProductStockItem.setUpdateBy(0L);
        // 更新时间
        nsProductStockItem.setUpdateTime(new Date());
        // 农场id
        nsProductStockItem.setRegionDeptId(0L);

        // 检查待保存数据的合法性
        nsProductStockItemApi.validatePo(nsProductStockItem);

        // 名称唯一性检查?
        // nsProductStockItemService.checkNameExisted(nsProductStockItem);

        NsProductStockItem result = nsProductStockItemService.saveNsProductStockItem(nsProductStockItem);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农产品库存项目表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProductStockItem");
        // 组件路径
        menu.setComponent("business/nsProductStockItem/nsProductStockItem");
        // 权限标识
        menu.setPerms("business:nsProductStockItem:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农产品库存项目表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农产品库存项目表-查询", 1, "business:nsProductStockItem:list");
            addOperationMenu(menu.getMenuId(), "农产品库存项目表-新增", 2, "business:nsProductStockItem:add");
            addOperationMenu(menu.getMenuId(), "农产品库存项目表-修改", 3, "business:nsProductStockItem:edit");
            addOperationMenu(menu.getMenuId(), "农产品库存项目表-删除", 4, "business:nsProductStockItem:remove");
            addOperationMenu(menu.getMenuId(), "农产品库存项目表-导出", 5, "business:nsProductStockItem:export");
            addOperationMenu(menu.getMenuId(), "农产品库存项目表-导入", 6, "business:nsProductStockItem:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProductStockItem ok");
        }
    }

    /**
     * 创建操作按钮权限（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农产品库存项目表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProductStockItem menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
