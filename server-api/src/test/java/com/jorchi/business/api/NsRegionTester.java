package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsRegion;
import com.jorchi.business.service.NsRegionService;
import com.jorchi.business.form.NsRegionForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_region-模块测试<br/>
 * 对应表名：ns_region，表备注：区块
 * @author: TanShunFu at jorchi
 * @date: 2022-11-09 14:14:51
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsRegionTester extends BaseApi {

    @Resource
    NsRegionService nsRegionService;

    @Resource
    NsRegionApi nsRegionApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @Test
    public void findByIdNsRegionTest() {
        Long regionId;
        regionId = 1L;

        NsRegion bean = nsRegionService.findById(regionId);
        if (bean == null)
            throw ClientException.of("NsRegion not found error!");

        log.info("findById NsRegionTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @Test
    public void listPageNsRegionTest() {
        NsRegionForm nsRegionVo = new NsRegionForm();
        nsRegionVo.setPageNum(1);
        nsRegionVo.setPageSize(10);
        log.info("listPage NsRegionTest result:");
        log.info(getDataTable(nsRegionService.listPage(nsRegionVo)).toString());
    }

    /**
     * 删除测试（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @Test
    public void deleteNsRegionTest() {
        Long id = 0L;
        log.info("delete NsRegionTest result:");
        log.info(AjaxResult.success(nsRegionService.deleteNsRegion(id)).toString());
    }

    /**
     * 新增或保存测试（区块）<br>
     * ID 为空即为新增，否则为更新
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @Test
    public void saveNsRegionTest () {
        NsRegion nsRegion = new NsRegion();
        // 区块ID
        // nsRegion.setRegionId(0L);
        // 区块名称
        nsRegion.setRegionName("区块名称");
        // 区块面积
        nsRegion.setRegionArea("区块面积");
        // 区域负责人ID
        nsRegion.setRegionLeaderId(0L);
        // 区域负责人姓名
        nsRegion.setRegionLeaderName("区域负责人姓名");
        // 区域坐标点
        nsRegion.setRegionPoints("区域坐标点");
        // 删除标志（0代表存在2代表删除）
        nsRegion.setDeleted(0);
        // 创建人
        nsRegion.setCreateBy(0L);
        // 创建时间
        nsRegion.setCreateTime(new Date());
        // 更新人
        nsRegion.setUpdateBy(0L);
        // 更新时间
        nsRegion.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsRegionApi.validatePo(nsRegion);

        // 名称唯一性检查?
        // nsRegionService.checkNameExisted(nsRegion);

        NsRegion result = nsRegionService.saveNsRegion(nsRegion);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("区块");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsRegion");
        // 组件路径
        menu.setComponent("business/nsRegion/nsRegion");
        // 权限标识
        menu.setPerms("business:nsRegion:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("TanShunFu");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("区块", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "区块-查询", 1, "business:nsRegion:list");
            addOperationMenu(menu.getMenuId(), "区块-新增", 2, "business:nsRegion:add");
            addOperationMenu(menu.getMenuId(), "区块-修改", 3, "business:nsRegion:edit");
            addOperationMenu(menu.getMenuId(), "区块-删除", 4, "business:nsRegion:remove");
            addOperationMenu(menu.getMenuId(), "区块-导出", 5, "business:nsRegion:export");
            addOperationMenu(menu.getMenuId(), "区块-导入", 6, "business:nsRegion:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsRegion ok");
        }
    }

    /**
     * 创建操作按钮权限（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("区块", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsRegion menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
