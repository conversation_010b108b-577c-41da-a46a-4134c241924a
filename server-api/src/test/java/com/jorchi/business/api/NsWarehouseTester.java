package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsWarehouse;
import com.jorchi.business.service.NsWarehouseService;
import com.jorchi.business.form.NsWarehouseForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_warehouse-模块测试<br/>
 * 对应表名：ns_warehouse，表备注：仓库表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-01 11:14:24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsWarehouseTester extends BaseApi {

    @Resource
    NsWarehouseService nsWarehouseService;

    @Resource
    NsWarehouseApi nsWarehouseApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    @Test
    public void findByIdNsWarehouseTest() {
        Long warehouseId;
        warehouseId = 1L;

        NsWarehouse bean = nsWarehouseService.findById(warehouseId);
        if (bean == null)
            throw ClientException.of("NsWarehouse not found error!");

        log.info("findById NsWarehouseTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    @Test
    public void listPageNsWarehouseTest() {
        NsWarehouseForm nsWarehouseVo = new NsWarehouseForm();
        nsWarehouseVo.setPageNum(1);
        nsWarehouseVo.setPageSize(10);
        log.info("listPage NsWarehouseTest result:");
        log.info(getDataTable(nsWarehouseService.listPage(nsWarehouseVo)).toString());
    }

    /**
     * 删除测试（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    @Test
    public void deleteNsWarehouseTest() {
        Long id = 0L;
        log.info("delete NsWarehouseTest result:");
        log.info(AjaxResult.success(nsWarehouseService.deleteNsWarehouse(id)).toString());
    }

    /**
     * 新增或保存测试（仓库表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    @Test
    public void saveNsWarehouseTest () {
        NsWarehouse nsWarehouse = new NsWarehouse();
        // 仓库ID
        // nsWarehouse.setWarehouseId(0L);
        // 分类ID
        // nsWarehouse.setCategoryId(0L);
        // 仓库编号
        nsWarehouse.setWarehouseCode("仓库编号");
        // 仓库名称
        nsWarehouse.setWarehouseName("仓库名称");
        // 位置
        nsWarehouse.setLocation("位置");
        // 存量
        nsWarehouse.setStockQuantity(0);
        // 环境要求
        nsWarehouse.setEnvironmentalReq("环境要求");
        // 设备设施
        nsWarehouse.setFacilities("设备设施");
        // 温度控制
        nsWarehouse.setTemperatureCtrl("温度控制");
        // 删除标志
        nsWarehouse.setDeleted(0);
        // 创建人
        nsWarehouse.setCreateBy(0L);
        // 创建时间
        nsWarehouse.setCreateTime(new Date());
        // 更新人
        nsWarehouse.setUpdateBy(0L);
        // 更新时间
        nsWarehouse.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsWarehouseApi.validatePo(nsWarehouse);

        // 名称唯一性检查?
        // nsWarehouseService.checkNameExisted(nsWarehouse);

        NsWarehouse result = nsWarehouseService.saveNsWarehouse(nsWarehouse);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("仓库表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsWarehouse");
        // 组件路径
        menu.setComponent("business/nsWarehouse/nsWarehouse");
        // 权限标识
        menu.setPerms("business:nsWarehouse:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("仓库表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "仓库表-查询", 1, "business:nsWarehouse:list");
            addOperationMenu(menu.getMenuId(), "仓库表-新增", 2, "business:nsWarehouse:add");
            addOperationMenu(menu.getMenuId(), "仓库表-修改", 3, "business:nsWarehouse:edit");
            addOperationMenu(menu.getMenuId(), "仓库表-删除", 4, "business:nsWarehouse:remove");
            addOperationMenu(menu.getMenuId(), "仓库表-导出", 5, "business:nsWarehouse:export");
            addOperationMenu(menu.getMenuId(), "仓库表-导入", 6, "business:nsWarehouse:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsWarehouse ok");
        }
    }

    /**
     * 创建操作按钮权限（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("仓库表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsWarehouse menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
