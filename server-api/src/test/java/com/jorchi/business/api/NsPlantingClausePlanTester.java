package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPlantingClausePlan;
import com.jorchi.business.service.NsPlantingClausePlanService;
import com.jorchi.business.form.NsPlantingClausePlanForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_planting_clause_plan-模块测试<br/>
 * 对应表名：ns_planting_clause_plan，表备注：种植项生产计划
 * @author: ChenLiFeng at jorchi
 * @date: 2022-11-11 14:18:18
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPlantingClausePlanTester extends BaseApi {

    @Resource
    NsPlantingClausePlanService nsPlantingClausePlanService;

    @Resource
    NsPlantingClausePlanApi nsPlantingClausePlanApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（种植项生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 14:18:18
     */
    @Test
    public void findByIdNsPlantingClausePlanTest() {
        Long plantingClauseId;
        plantingClauseId = 1L;

        NsPlantingClausePlan bean = nsPlantingClausePlanService.findById(plantingClauseId);
        if (bean == null)
            throw ClientException.of("NsPlantingClausePlan not found error!");

        log.info("findById NsPlantingClausePlanTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（种植项生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 14:18:18
     */
    @Test
    public void listPageNsPlantingClausePlanTest() {
        NsPlantingClausePlanForm nsPlantingClausePlanVo = new NsPlantingClausePlanForm();
        nsPlantingClausePlanVo.setPageNum(1);
        nsPlantingClausePlanVo.setPageSize(10);
        log.info("listPage NsPlantingClausePlanTest result:");
        log.info(getDataTable(nsPlantingClausePlanService.listPage(nsPlantingClausePlanVo)).toString());
    }

    /**
     * 删除测试（种植项生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 14:18:18
     */
    @Test
    public void deleteNsPlantingClausePlanTest() {
        Long id = 0L;
        log.info("delete NsPlantingClausePlanTest result:");
        log.info(AjaxResult.success(nsPlantingClausePlanService.deleteNsPlantingClausePlan(id)).toString());
    }

    /**
     * 新增或保存测试（种植项生产计划）<br>
     * ID 为空即为新增，否则为更新
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 14:18:18
     */
//    @Test
//    public void saveNsPlantingClausePlanTest () {
//        NsPlantingClausePlan nsPlantingClausePlan = new NsPlantingClausePlan();
//        // 种植项ID
//        // nsPlantingClausePlan.setPlantingClauseId(0L);
//        // 种植标准ID
//        // nsPlantingClausePlan.setPlantingStandardId(0L);
//        // 农事流程ID
//        nsPlantingClausePlan.setFarmingProcessId(0L);
//        // 农事流程名称
//        nsPlantingClausePlan.setFarmingProcessName("农事流程名称");
//        // 作业时间
//        nsPlantingClausePlan.setInputTime(new BigDecimal("0"));
//        // 是否重复执行
//        nsPlantingClausePlan.setIsRepeat(0);
//        // 周期开始
//        nsPlantingClausePlan.setCycleStart(0);
//        // 周期结束
//        nsPlantingClausePlan.setCycleEnd(0);
//        // 是否选择物料
//        nsPlantingClausePlan.setIsSelect(0);
//        // 内容详情
//        nsPlantingClausePlan.setContext("内容详情");
//        // 删除标志
//        nsPlantingClausePlan.setDeleted(0);
//        // 创建人
//        nsPlantingClausePlan.setCreateBy(0L);
//        // 创建时间
//        nsPlantingClausePlan.setCreateTime(new Date());
//        // 更新人
//        nsPlantingClausePlan.setUpdateBy(0L);
//        // 更新时间
//        nsPlantingClausePlan.setUpdateTime(new Date());
//
//        // 检查待保存数据的合法性
//        nsPlantingClausePlanApi.validatePo(nsPlantingClausePlan);
//
//        // 名称唯一性检查?
//        // nsPlantingClausePlanService.checkNameExisted(nsPlantingClausePlan);
//
//        NsPlantingClausePlan result = nsPlantingClausePlanService.saveNsPlantingClausePlan(nsPlantingClausePlan);
//        log.info("save result:");
//        log.info(result.toString());
//    }


    /**
     * 生成菜单（种植项生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 14:18:18
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("种植项生产计划");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPlantingClausePlan");
        // 组件路径
        menu.setComponent("business/nsPlantingClausePlan/nsPlantingClausePlan");
        // 权限标识
        menu.setPerms("business:nsPlantingClausePlan:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("ChenLiFeng");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("种植项生产计划", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "种植项生产计划-查询", 1, "business:nsPlantingClausePlan:list");
            addOperationMenu(menu.getMenuId(), "种植项生产计划-新增", 2, "business:nsPlantingClausePlan:add");
            addOperationMenu(menu.getMenuId(), "种植项生产计划-修改", 3, "business:nsPlantingClausePlan:edit");
            addOperationMenu(menu.getMenuId(), "种植项生产计划-删除", 4, "business:nsPlantingClausePlan:remove");
//            addOperationMenu(menu.getMenuId(), "种植项生产计划-导出", 5, "business:nsPlantingClausePlan:export");
//            addOperationMenu(menu.getMenuId(), "种植项生产计划-导入", 6, "business:nsPlantingClausePlan:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPlantingClausePlan ok");
        }
    }

    /**
     * 创建操作按钮权限（种植项生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 14:18:18
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（种植项生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 14:18:18
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("种植项生产计划", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPlantingClausePlan menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
