package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPersonUnion;
import com.jorchi.business.service.NsPersonUnionService;
import com.jorchi.business.form.NsPersonUnionForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_person_union-模块测试<br/>
 * 对应表名：ns_person_union，表备注：技术人员、代理人员关联表
 * @author: xubinbin at jorchi
 * @date: 2022-11-21 18:04:20
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPersonUnionTester extends BaseApi {

    @Resource
    NsPersonUnionService nsPersonUnionService;

    @Resource
    NsPersonUnionApi nsPersonUnionApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（技术人员、代理人员关联表）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-21 18:04:20
     */
/*    @Test
    public void findByIdNsPersonUnionTest() {
        Long unionId;
        unionId = 1L;

        NsPersonUnion bean = nsPersonUnionService.findById(unionId);
        if (bean == null)
            throw ClientException.of("NsPersonUnion not found error!");

        log.info("findById NsPersonUnionTest result:");
        log.info(bean.toString());
    }*/

    /**
     * 分页查询测试（技术人员、代理人员关联表）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-21 18:04:20
     */
/*    @Test
    public void listPageNsPersonUnionTest() {
        NsPersonUnionForm nsPersonUnionVo = new NsPersonUnionForm();
        nsPersonUnionVo.setPageNum(1);
        nsPersonUnionVo.setPageSize(10);
        log.info("listPage NsPersonUnionTest result:");
        log.info(getDataTable(nsPersonUnionService.listPage(nsPersonUnionVo)).toString());
    }*/

    /**
     * 删除测试（技术人员、代理人员关联表）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-21 18:04:20
     */
/*    @Test
    public void deleteNsPersonUnionTest() {
        Long id = 0L;
        log.info("delete NsPersonUnionTest result:");
        log.info(AjaxResult.success(nsPersonUnionService.deleteNsPersonUnion(id)).toString());
    }*/

    /**
     * 新增或保存测试（技术人员、代理人员关联表）<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2022-11-21 18:04:20
     */
/*    @Test
    public void saveNsPersonUnionTest () {
        NsPersonUnion nsPersonUnion = new NsPersonUnion();
        // 联合表主键
        // nsPersonUnion.setUnionId(0L);
        // 技术人员id
        // nsPersonUnion.setTechnicalId(0L);
        // 代理人员id
        nsPersonUnion.setAgentId(0L);
        // 删除标志（0代表存在2代表删除）
        nsPersonUnion.setDeleted(0);
        // 任务ID
        nsPersonUnion.setTaskId(0L);
        // 创建人
        nsPersonUnion.setCreateBy(0L);
        // 创建时间
        nsPersonUnion.setCreateTime(new Date());
        // 更新人
        nsPersonUnion.setUpdateBy(0L);
        // 更新时间
        nsPersonUnion.setUpdateTime(new Date());

*//*        // 检查待保存数据的合法性
        nsPersonUnionApi.validatePo(nsPersonUnion);*//*

        // 名称唯一性检查?
        // nsPersonUnionService.checkNameExisted(nsPersonUnion);

*//*        NsPersonUnion result = nsPersonUnionService.saveNsPersonUnion(nsPersonUnion);
        log.info("save result:");
        log.info(result.toString());*//*
}*/


    /**
     * 生成菜单（技术人员、代理人员关联表）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-21 18:04:20
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("技术人员、代理人员关联表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPersonUnion");
        // 组件路径
        menu.setComponent("business/nsPersonUnion/nsPersonUnion");
        // 权限标识
        menu.setPerms("business:nsPersonUnion:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("技术人员、代理人员关联表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "技术人员、代理人员关联表-查询", 1, "business:nsPersonUnion:list");
            addOperationMenu(menu.getMenuId(), "技术人员、代理人员关联表-新增", 2, "business:nsPersonUnion:add");
            addOperationMenu(menu.getMenuId(), "技术人员、代理人员关联表-修改", 3, "business:nsPersonUnion:edit");
            addOperationMenu(menu.getMenuId(), "技术人员、代理人员关联表-删除", 4, "business:nsPersonUnion:remove");
            addOperationMenu(menu.getMenuId(), "技术人员、代理人员关联表-导出", 5, "business:nsPersonUnion:export");
            addOperationMenu(menu.getMenuId(), "技术人员、代理人员关联表-导入", 6, "business:nsPersonUnion:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPersonUnion ok");
        }
    }

    /**
     * 创建操作按钮权限（技术人员、代理人员关联表）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-21 18:04:20
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（技术人员、代理人员关联表）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-21 18:04:20
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("技术人员、代理人员关联表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPersonUnion menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
