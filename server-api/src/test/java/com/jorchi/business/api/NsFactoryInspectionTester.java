package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsFactoryInspection;
import com.jorchi.business.service.NsFactoryInspectionService;
import com.jorchi.business.form.NsFactoryInspectionForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_factory_inspection-模块测试<br/>
 * 对应表名：ns_factory_inspection，表备注：出厂检验表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-16 10:38:50
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsFactoryInspectionTester extends BaseApi {

    @Resource
    NsFactoryInspectionService nsFactoryInspectionService;

    @Resource
    NsFactoryInspectionApi nsFactoryInspectionApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（出厂检验表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-16 10:38:50
     */
    @Test
    public void findByIdNsFactoryInspectionTest() {
        Long inspectionId;
        inspectionId = 1L;

        NsFactoryInspection bean = nsFactoryInspectionService.findById(inspectionId);
        if (bean == null)
            throw ClientException.of("NsFactoryInspection not found error!");

        log.info("findById NsFactoryInspectionTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（出厂检验表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-16 10:38:50
     */
    @Test
    public void listPageNsFactoryInspectionTest() {
        NsFactoryInspectionForm nsFactoryInspectionVo = new NsFactoryInspectionForm();
        nsFactoryInspectionVo.setPageNum(1);
        nsFactoryInspectionVo.setPageSize(10);
        log.info("listPage NsFactoryInspectionTest result:");
        log.info(getDataTable(nsFactoryInspectionService.listPage(nsFactoryInspectionVo)).toString());
    }

    /**
     * 删除测试（出厂检验表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-16 10:38:50
     */
    @Test
    public void deleteNsFactoryInspectionTest() {
        Long id = 0L;
        log.info("delete NsFactoryInspectionTest result:");
        log.info(AjaxResult.success(nsFactoryInspectionService.deleteNsFactoryInspection(id)).toString());
    }

    /**
     * 新增或保存测试（出厂检验表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-16 10:38:50
     */
    @Test
    public void saveNsFactoryInspectionTest () {
        NsFactoryInspection nsFactoryInspection = new NsFactoryInspection();
        // 检验记录ID
        // nsFactoryInspection.setInspectionId(0L);
        // 检验人
        nsFactoryInspection.setOperator("检验人");
        // 检验人id
        nsFactoryInspection.setOperatorId(0L);
        // 关联生产计划
        nsFactoryInspection.setProductionPlanId(0L);
        // 检验日期
        nsFactoryInspection.setInspectionDate(new Date());
        // 抽样数量
        nsFactoryInspection.setSampleQuantity(0);
        // 农产品id
        nsFactoryInspection.setProductStockId(0L);
        // 品种
        nsFactoryInspection.setBreeds("品种");
        // 农产品名称
        nsFactoryInspection.setCropName("农产品名称");
        // 规格
        nsFactoryInspection.setSpecification("规格");
        // 记录编号
        nsFactoryInspection.setRecordNumber("记录编号");
        // 产品收货批号
        nsFactoryInspection.setProductBatchNumber("产品收货批号");
        // 车辆运输编号
        nsFactoryInspection.setVehicleTransportNumber("车辆运输编号");
        // 检验内容
        nsFactoryInspection.setInspectionContent("检验内容");
        // 结果：1-及格，2-不及格
        nsFactoryInspection.setResult(0);

        // 检查待保存数据的合法性
        nsFactoryInspectionApi.validatePo(nsFactoryInspection);

        // 名称唯一性检查?
        // nsFactoryInspectionService.checkNameExisted(nsFactoryInspection);

        NsFactoryInspection result = nsFactoryInspectionService.saveNsFactoryInspection(nsFactoryInspection);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（出厂检验表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-16 10:38:50
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("出厂检验表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsFactoryInspection");
        // 组件路径
        menu.setComponent("business/nsFactoryInspection/nsFactoryInspection");
        // 权限标识
        menu.setPerms("business:nsFactoryInspection:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("出厂检验表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "出厂检验表-查询", 1, "business:nsFactoryInspection:list");
            addOperationMenu(menu.getMenuId(), "出厂检验表-新增", 2, "business:nsFactoryInspection:add");
            addOperationMenu(menu.getMenuId(), "出厂检验表-修改", 3, "business:nsFactoryInspection:edit");
            addOperationMenu(menu.getMenuId(), "出厂检验表-删除", 4, "business:nsFactoryInspection:remove");
            addOperationMenu(menu.getMenuId(), "出厂检验表-导出", 5, "business:nsFactoryInspection:export");
            addOperationMenu(menu.getMenuId(), "出厂检验表-导入", 6, "business:nsFactoryInspection:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsFactoryInspection ok");
        }
    }

    /**
     * 创建操作按钮权限（出厂检验表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-16 10:38:50
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（出厂检验表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-16 10:38:50
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("出厂检验表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsFactoryInspection menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
