//package com.jorchi.system;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jorchi.ApplicationFast;
//import com.jorchi.framework.web.api.BaseApi;
//import com.jorchi.framework.web.page.TableDataInfo;
//import com.jorchi.project.monitor.dao.SysOperLogDao;
//import com.jorchi.project.monitor.domain.SysOperLog;
//import com.jorchi.project.monitor.service.SysOperLogServiceImpl;
//import com.jorchi.server.dao.PolicyDao;
//import com.jorchi.server.po.Policy;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import pdfc.framework.common.ResultPage;
////import pdfc.framework.mybatis.Page;
////import pdfc.framework.mybatis.PageParam;
//import pdfc.framework.mybatis.util.Pages;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * 分页测试
// *
// * <AUTHOR> Sugar.Tan
// * @date : 2021-03-23 18:38
// *
// * 结论：在 server-api 模块只能用老版本分页；其它模块只能用PDFC-版本 翻页
// * 有时间可以统一一下
// */
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ApplicationFast.class)
//public class PageTest extends BaseApi {
//    @Resource
//    private SysOperLogDao operLogMapper;
//
//    /**
//     * 老版本分页
//     */
//    @Test
//    public void pageList() {
//        startPage();
//        SysOperLog operLog = new SysOperLog();
//        List<SysOperLog> list = operLogMapper.selectOperLogList(operLog);
//        TableDataInfo pageData = getDataTable(list);
//
//        String json = JSONObject.toJSONString(pageData);
//        System.out.println(json);
//    }
//
////    @Test
////    public void pageListByPdfc() {
////        SysOperLog operLog = new SysOperLog();
////        PageParam pageParam = new PageParam(1, 5);
////        //Page<Policy> list_ = policyDao.selectPage(pageParam, policy);
////        Page<SysOperLog> list_ = operLogMapper.selectPage(pageParam, operLog);
////        System.out.println(">>>>>>>>>>>>>>>>>> page one:");
////
////        //display(list_);
////        //System.out.println(JSONObject.toJSONString(list_));
////        //System.out.println(list_.getTotalCount());
////
////        ResultPage<Policy> pageRe = Pages.convert(pageParam, list_, Policy.class);
////        System.out.println(pageRe);
////
////        pageParam = new PageParam(2, 5);
////        Page<SysOperLog> list2_ = operLogMapper.selectPage(pageParam, operLog);
////        //display(list2_);
////
////        ResultPage<Policy> pageRe2 = Pages.convert(pageParam, list2_, Policy.class);
////        System.out.println(pageRe2);
////    }
//
//
//    @Resource
//    PolicyDao policyDao;
//
//    /**
//     * PDFC-版本 翻页
//     */
////    @Test
////    public void list() {
////        Policy policy = new Policy();
////        PageParam pageParam = new PageParam(1, 5);
////        /*Page<Policy> list_ = policyDao.selectPage(pageParam, policy);
////        System.out.println(">>>>>>>>>>>>>>>>>> page one:");*/
////
////        //display(list_);
////        //System.out.println(JSONObject.toJSONString(list_));
////        //System.out.println(list_.getTotalCount());
////
////        ResultPage<Policy> pageRe = Pages.convert(pageParam, list_, Policy.class);
////        System.out.println(pageRe);
////
////        pageParam = new PageParam(2, 5);
////        Page<Policy> list2_ = policyDao.selectPage(pageParam, policy);
////        //display(list2_);
////
////        ResultPage<Policy> pageRe2 = Pages.convert(pageParam, list2_, Policy.class);
////        System.out.println(pageRe2);
////    }
//
//}
