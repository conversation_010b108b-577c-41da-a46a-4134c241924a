package com.jorchi.system;

import com.jorchi.ApplicationFast;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.api.SysMenuApi;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * 龙湖微服务测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-22 14:33
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
public class SysMenuTest {

    @Autowired
    SysMenuApi sysMenuApi;


    @Test
    public void list() {
        AjaxResult menus = sysMenuApi.listByGuest();
        System.out.println(menus);
    }
}
