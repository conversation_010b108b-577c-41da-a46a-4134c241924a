package com.jorchi.common;

import org.junit.Test;
import pdfc.framework.utils.Beans;

import java.util.Date;

/**
 * 对象copy测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-20 21:26
 */
public class BeansTest {

    /**
     * 验证 Beans.copy 是忽略大小写不忽略下划线
     */
    @Test
    public void copyIngoreCase() {
        BeanA a = new BeanA();
        a.setCreateTime(new Date());
        a.setUpdateTime(new Date());
        a.setCan_not_copy("can not copy");

        BeanB b = new BeanB();
        Beans.copy(a, b);
        System.out.println(b.getCreatetime());
        System.out.println(b.getUpdateTime());
        System.out.println(b.getCanNotCopy());
    }

    /**
     * 深度拷贝一个对象
     */
   /* @Test
    public void copyDepthTest() {
        BeanA a = new BeanA();
        a.setCreateTime(new Date());
        a.setUpdateTime(new Date());
        a.setCan_not_copy("can not copy");

        // BeanB b = new BeanB();
        // Beans.copy(a, b);
        BeanB b = Beans.copyDepth().from(a).to(BeanB.class);

        BeanB c = Beans.copyDepth().from(a).to(BeanB.class);
        System.out.println(b.getCreatetime());
        System.out.println(b.getUpdateTime());
        System.out.println(b.getCanNotCopy());
        System.out.println(c);
    }*/
}
