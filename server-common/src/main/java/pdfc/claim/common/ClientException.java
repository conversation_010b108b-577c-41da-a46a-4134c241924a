package pdfc.claim.common;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class ClientException extends RuntimeException {
    static final Log log = LogFactory.getLog(ClientException.class);

    /** 出错消息 */
    private String message;
    /** 出错状态码 */
    private int errorCode;


    public ClientException() {}
    public ClientException(String message) {
        this.message = message;
        this.errorCode = ErrorCode.CLIENT_ERROR;
        log.error(CommonUtil.append(CommonUtil.whoCalledMe(), message));
    }

    public ClientException(int errorCode, Object ... message) {
        this.message = CommonUtil.append(message);
        this.errorCode = errorCode;
        log.error(CommonUtil.append(CommonUtil.whoCalledMe(), this.message));
    }

    public static ClientException of(Object ... message) {
        ClientException that = new ClientException();
        that.message = CommonUtil.append(message);
        that.errorCode = ErrorCode.CLIENT_ERROR;
        log.error(CommonUtil.append(CommonUtil.whoCalledMe(), that.message));
        return that;
    }

    public static ClientException ofCodeAndMsg(long errorCode, Object ... message) {
        ClientException that = new ClientException();
        that.message = CommonUtil.append(message);
        that.errorCode = ErrorCode.CLIENT_ERROR;
        log.error(CommonUtil.append(CommonUtil.whoCalledMe(), that.message));
        return that;
    }

    public ClientException setMessage(Object ... message) {
        this.message = CommonUtil.append(message);
        return this;
    }
    public String getMessage() {
        return message;
    }

    public int getErrorCode() {
        return errorCode;
    }

    public ClientException setErrorCode(int errorCode) {
        this.errorCode = errorCode;
        return this;
    }
}
