
package pdfc.framework.utils;

import java.util.AbstractMap;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;

public final class Maps {
    private Maps() {
    }

    public static <K, V> Map<K, V> caseInsensitiveMap(final Map<K, V> source) {
        final Map<String, V> lowerCaseMap = new HashMap();
        Iterator var2 = source.entrySet().iterator();

        while(var2.hasNext()) {
            Entry<K, V> entry = (Entry)var2.next();
            Object key = entry.getKey();
            if (key instanceof String) {
                lowerCaseMap.put(toLowercase(key), source.get(key));
            }
        }

        return new AbstractMap<K, V>() {
            public Set<Entry<K, V>> entrySet() {
                return source.entrySet();
            }

            public boolean containsKey(Object key) {
                return key instanceof String ? lowerCaseMap.containsKey(Maps.toLowercase(key)) : source.containsKey(key);
            }

            public V get(Object key) {
                return key instanceof String ? lowerCaseMap.get(Maps.toLowercase(key)) : source.get(key);
            }

            public V put(K key, V value) {
                if (key instanceof String) {
                    String lowercaseKey = Maps.toLowercase(key);
                    source.put(key, value);
                    return lowerCaseMap.put(lowercaseKey, value);
                } else {
                    return source.put(key, value);
                }
            }
        };
    }

    private static String toLowercase(Object key) {
        return key.toString().toLowerCase(Locale.getDefault());
    }
}
