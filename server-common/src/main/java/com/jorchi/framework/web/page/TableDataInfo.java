package com.jorchi.framework.web.page;

import com.github.pagehelper.PageInfo;
import com.jorchi.common.constant.HttpStatus;
import lombok.Data;
//import pdfc.framework.mybatis.Page;

import java.io.Serializable;
import java.util.List;

/**
 * 表格分页数据对象
 *
 * <AUTHOR>
 */
@Data
public class TableDataInfo<T> implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 总记录数 */
    private long total;

    /** 列表数据 */
    // private List<?> rows;
    private Object rows;

    /** 消息状态码 */
    private int code;

    /** 消息内容 */
    private String msg;

    /**
     * 表格数据对象
     */
    public TableDataInfo()
    {
    }

    /**
     * 分页
     *
     * @param list 列表数据
     * @param total 总记录数
     */
    public TableDataInfo(Object list, int total)
    {
        this.rows = list;
        this.total = total;
    }


//    /**
//     * 响应请求分页数据
//     */
//    public static TableDataInfo of(Page<?> list)
//    {
//        TableDataInfo rspData = new TableDataInfo();
//        rspData.setCode(HttpStatus.SUCCESS);
//        rspData.setMsg("查询成功");
//        rspData.setRows(list);
//        rspData.setTotal(list.getPaginator() == null ? -1 :list.getPaginator().getTotalCount());
//        return rspData;
//    }

    public static TableDataInfo of(List<?> list)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

}
