package com.jorchi.common.constant;


import pdfc.claim.common.CommonUtil;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants
{
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    public static final String REDIS_PRE_FIX = "farmA:";

    public static final String CODE_ERROR_NUM = "codeError:";
    public static final String PHONE_CODE_FIX = "phoneCode:";
    public static final String PHONE_CODE_ERROR = "phoneErreoCode:";
    public static final String DAY_END_KEY = REDIS_PRE_FIX + "_DayEnd";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = CommonUtil.append(REDIS_PRE_FIX, "captcha_codes:");

    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = CommonUtil.append(REDIS_PRE_FIX, "login_tokens:");

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = CommonUtil.append(REDIS_PRE_FIX, "repeat_submit:");
    /**
     * 该案件正在上传理赔
     */
    public static final String UPLOADING_KEY = CommonUtil.append(REDIS_PRE_FIX, "uploading:");
    /**
     * 该案件已上传理赔
     */
    public static final String UPLOADED_KEY = CommonUtil.append(REDIS_PRE_FIX, "uploaded:");

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 90;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = CommonUtil.append(REDIS_PRE_FIX, "login_user_key");

    /**
     * 用户ID
     */
    //public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    //public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    //public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    //public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    //public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = CommonUtil.append(REDIS_PRE_FIX, "sys_config:");

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = CommonUtil.append(REDIS_PRE_FIX, "ns_sys_dict:");
    public static final String SYS_DICT_DATA = CommonUtil.append(REDIS_PRE_FIX, "ns_dict_data:");

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * 资源映射路径 前缀
     */
    //public static final String COM_PREFIX = "com_prefix";

    /**
     * 与被保险人关系
     */
    public static final String RELATION_SHIP = "relation_ship";

    /**
     * 出险原因
     */
    public static final String DAMAGE_CODE = "damage_code";

    /**
     * 赔案案件状态
     */
    public static final String REGIST_STATUS = "regist_status";

    /**
     * 案件紧急状态
     */
    public static final String CASE_IFMERCY = "ifmercy";

    /**
     * 案件类型/赔付类型
     */
    public static final String CASE_TYPE = "type";

    /**
     * 领款人证件类型
     */
    public static final String IDENTIFY_TYPE = "identify_type";

    /**
     * 领款人类型
     */
    public static final String CUSTOMER_TYPE_CODE = "customer_type_code";

    public static String IP_ERROR = REDIS_PRE_FIX + "DangerCode:";
}
