package com.jorchi.common.utils;

import com.jorchi.common.exception.BaseException;
import org.apache.commons.collections.CollectionUtils;


import java.util.Collection;

/**
 * FileName: FsAssert.java
 * Description: 断言
 *
 * <AUTHOR>  GeSenQi
 * @version : 1.0
 * Create at: 2022-11-02 22:29
 * <p>
 * Modification History:
 * Date         Author      Version      Description
 * -----------------------------------------------------------------
 * 2022/11/2   GeSenQi       1.0         1.0 Version
 */
public class FsAssert {

    FsAssert() {

    }

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new BaseException(message);
        }
    }

    public static void isFalse(boolean expression, String message) {
        isTrue(!expression, message);
    }

    public static void isNull(Object object, String message) {
        if (object != null) {
            throw new BaseException(message);
        }
    }

    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new BaseException(message);
        }
    }

    public static void notEmpty(String text, String message) {
        if (StringUtils.isEmpty(text)) {
            throw new BaseException(message);
        }
    }

    public static void notEmpty(Collection<?> collection, String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BaseException(message);
        }
    }

    public static void isEmpty(String text, String message) {
        if (StringUtils.isNotEmpty(text)) {
            throw new BaseException(message);
        }
    }

    public static void isEmpty(Collection<?> collection, String message) {
        if (CollectionUtils.isNotEmpty(collection)) {
            throw new BaseException(message);
        }
    }
}
