package com.jorchi.common.utils;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


/**
 * 版本比较工具类
 */
public class VersionUtils
{
    private static final Log log = LogFactory.getLog(VersionUtils.class);

    public static int[] convertVersions(String version)
    {
        String[] subVersions = version.split("\\.");

        if (subVersions.length == 3) {
            return new int[]{
				Integer.valueOf(subVersions[0]),
				Integer.valueOf(subVersions[1]),
				Integer.valueOf(subVersions[2])};
        }

        if (subVersions.length == 4) {
            return new int[]{
				Integer.valueOf(subVersions[0]),
				Integer.valueOf(subVersions[1]),
				Integer.valueOf(subVersions[2]),
				Integer.valueOf(subVersions[3])};
        }

        log.error(version+" The version must be like this: 1.0.0");
        return null;
    }

    /** 比较版本号，是否比原版本更大 */
    public static boolean isNewVersion(int[] newVersion, int[] clientVersion)
    {
        if (
            // 先比大版本，大版本相等再比小版本
            newVersion[0] > clientVersion[0] ||
            newVersion[1] > clientVersion[1] && newVersion[0] == clientVersion[0] ||
            newVersion[2] > clientVersion[2] && newVersion[1] == clientVersion[1] && newVersion[0] == clientVersion[0]
        ) {
            return true;
        }

        if (newVersion.length == 3) {
            return false;
        }

        // 前3个大版本都相等（第4个版本号可不填）
        if (newVersion[2] == clientVersion[2] && newVersion[1] == clientVersion[1] && newVersion[0] == clientVersion[0]) {
            return newVersion.length == 4 && clientVersion.length == 3 ||
                    newVersion[3] > clientVersion[3];
        }
        return false;
    }
}
