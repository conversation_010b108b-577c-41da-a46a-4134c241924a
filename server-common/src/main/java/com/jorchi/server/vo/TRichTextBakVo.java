package com.jorchi.server.vo;

import java.io.Serializable;
import java.util.Date;
// // import io.swagger.annotations.ApiModel;
// // import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * TRichTextBakVo对象.对应实体描述：富文本内容（备份表）
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @ApiModel("TRichTextBakVo对象")
public class TRichTextBakVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：id,备注：主键 */
    // @ApiModelProperty("主键")
    private Long id;
    /** 对应字段：backup_time,备注：备份时间 */
    // @ApiModelProperty("备份时间")
    private Date backupTime;
    /** 对应字段：text,备注：富文本内容 */
    // @ApiModelProperty("富文本内容")
    private String text;
}
