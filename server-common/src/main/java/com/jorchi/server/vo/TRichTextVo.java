package com.jorchi.server.vo;

import java.io.Serializable;
import java.util.Date;
// // import io.swagger.annotations.ApiModel;
// // import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * TRichTextVo对象.对应实体描述：富文本内容
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @ApiModel("TRichTextVo对象")
public class TRichTextVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：id,备注：主键 */
    // @ApiModelProperty("主键")
    private Long id;
    /** 对应字段：text,备注：富文本内容 */
    // @ApiModelProperty("富文本内容")
    private String text;
    /** 对应字段：update_time,备注：操作时间 */
    // @ApiModelProperty("操作时间")
    private Date updateTime;
}
