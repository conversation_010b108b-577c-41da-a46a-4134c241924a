package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsHouseVo对象。对应实体描述：大棚
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsHouseVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "house_id", description = "大棚ID")
    private Long houseId;

    @Excel(name = "house_name", description = "大棚编号")
    private String houseName;

    @Excel(name = "house_region_id", description = "区块ID")
    private Long houseRegionId;

    @Excel(name = "house_region_name", description = "区块名称")
    private String houseRegionName;

    @Excel(name = "region_dept_id", description = "农场ID")
    private Long regionDeptId;

    @Excel(name = "region_dept_name", description = "农场名称")
    private String regionDeptName;

    @Excel(name = "house_area", description = "大棚面积")
    private String houseArea;

    @Excel(name = "technician_id", description = "生产技术人员ID")
    private Long technicianId;

    @Excel(name = "technician_name", description = "生产技术人员姓名")
    private String technicianName;

    @Excel(name = "technician_agent_id", description = "代理人ID")
    private Long technicianAgentId;

    @Excel(name = "technician_agent_name", description = "代理人名称")
    private String technicianAgentName;

    @Excel(name = "house_points", description = "区域坐标点")
    private String housePoints;

    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

}
