package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsSalesOrderItemVo对象。对应实体描述：销售订单子项表
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsSalesOrderItemVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "item_id", description = "子项ID")
    private Long itemId;

    @Excel(name = "order_id", description = "销售订单ID")
    private Long orderId;

    @Excel(name = "product_stock_id", description = "农产品ID")
    private Long productStockId;

    @Excel(name = "breeds", description = "品种")
    private String breeds;

    @Excel(name = "cropName", description = "农产品名称")
    private String cropName;

    @Excel(name = "unit_price", description = "销售单价")
    private BigDecimal unitPrice;

    @Excel(name = "unit", description = "单位")
    private String unit;

    @Excel(name = "quantity", description = "数量")
    private BigDecimal quantity;

    @Excel(name = "pay_amount", description = "金额")
    private BigDecimal payAmount;

    @Excel(name = "subtotal", description = "小计（unit_price*quantity）")
    private BigDecimal subtotal;

    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

}
