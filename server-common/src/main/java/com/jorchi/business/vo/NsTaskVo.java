package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsTaskVo对象。对应实体描述：任务表
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsTaskVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "task_id", description = "任务主键")
    private Long taskId;

    @Excel(name = "ns_production_plan_id", description = "生产计划id")
    private Long nsProductionPlanId;

    @Excel(name = "farming_process_id", description = "农事流程id")
    private Long farmingProcessId;

    @Excel(name = "process_name", description = "农事流程名称")
    private String processName;

    @Excel(name = "start_time", description = "流程开始时间")
    private Date startTime;

    @Excel(name = "end_time", description = "流程结束时间")
    private Date endTime;

    @Excel(name = "STATUS", description = "完成状态")
    private Boolean status;

    @Excel(name = "delay", description = "是否延期")
    private Boolean delay;

    @Excel(name = "output", description = "产量")
    private String output;

    @Excel(name = "recovered_area", description = "采收面积")
    private String recoveredArea;

    @Excel(name = "completion_time", description = "完成时间")
    private Date completionTime;

    @Excel(name = "cycle_start", description = "周期开始")
    private Integer cycleStart;

    @Excel(name = "cycle_end", description = "周期结束")
    private Integer cycleEnd;

    @Excel(name = "remarks", description = "备注")
    private String remarks;

    @Excel(name = "picture_url", description = "图片")
    private String pictureUrl;

    @Excel(name = "artisan_id", description = "技术人员id")
    private Long artisanId;

    @Excel(name = "artisan_name", description = "技术人员名字")
    private String artisanName;

    @Excel(name = "technician_agent_id", description = "代理人ID")
    private Long technicianAgentId;

    @Excel(name = "technician_agent_name", description = "代理人名称")
    private String technicianAgentName;

    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Boolean deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
