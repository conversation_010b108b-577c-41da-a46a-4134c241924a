package com.jorchi.business.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2022/11/28 14:46
 * @Describe: 移交任务审查请求参数
 */
@Data
public class TaskInsepectVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**是否通过：1表示通过，2表示拒绝*/
    private int inspectType;

    /**技术人员id*/
    private long artisanId;

    /**休假移交主键*/
    private long transferId;

}
