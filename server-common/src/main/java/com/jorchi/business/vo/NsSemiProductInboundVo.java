package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsSemiProductInboundVo对象。对应实体描述：半成品入库表
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsSemiProductInboundVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "inbound_id", description = "入库ID")
    private Long inboundId;

    @Excel(name = "warehouse_id", description = "入库ID")
    private Long warehouseId;

    @Excel(name = "picker", description = "采摘人")
    private Long picker;



    @Excel(name = "pick_date", description = "采摘日期")
    private Date pickDate;

    @Excel(name = "house_id", description = "来源大棚")
    private Long houseId;

    @Excel(name = "production_plan_id", description = "生产计划")
    private Long productionPlanId;

    @Excel(name = "actual_quantity", description = "实际入库量")
    private BigDecimal actualQuantity;

    @Excel(name = "converted", description = "是否已转换成品")
    private Boolean converted;

    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    // 大棚编号
    @Excel(name = "house_code", description = "大棚编号")
    private String houseName;

    // 仓库编
    @Excel(name = "warehouse_code", description = "仓库编号")
    private String warehouseCode;

    @Excel(name = "crop_name", description = "作物名称")
    private String cropName;

    @Excel(name = "crop_type", description = "作物类型")
    private String cropType;

    @Excel(name = "production_plan_code", description = "生产计划编号")
    private String productionPlanCode;

    @Excel(name = "picker_name", description = "采摘人")
    private String pickerName;

    @Excel(name = "items", description = "采摘明细")
    private String items;
}
