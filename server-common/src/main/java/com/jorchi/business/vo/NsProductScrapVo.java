package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsProductScrapVo对象。对应实体描述：报废表
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsProductScrapVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "scrap_id", description = "报废id")
    private Long scrapId;

    @Excel(name = "applicant", description = "申请人")
    private Long applicant;

    @Excel(name = "applicant_name", description = "申请人姓名")
    private String applicantName;

    @Excel(name = "type", description = "业务类型，下拉选项框，选项值为：1.半成品入库2.半成品转成品入库3.销售退库")
    private Integer type;

    @Excel(name = "warehouse_id", description = "仓库id")
    private String warehouseId;

    @Excel(name = "warehouse_code", description = "仓库编号")
    private String warehouseCode;

    @Excel(name = "order_id", description = "销售订单号")
    private String orderId;

    @Excel(name = "production_plan_id", description = "生产计划id")
    private Long productionPlanId;

    @Excel(name = "semi_product_id", description = "半成品入库id")
    private Long semiProductId;

    @Excel(name = "product_id", description = "成品id")
    private Long productId;

    @Excel(name = "crop_type", description = "农产品类别")
    private String cropType;

    @Excel(name = "crop_name", description = "农产品名称")
    private String cropName;

    @Excel(name = "scrap_quantity", description = "报废数量")
    private BigDecimal scrapQuantity;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

}
