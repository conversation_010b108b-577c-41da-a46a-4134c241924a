package com.jorchi.business.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: xubinbin
 * @Date: 2023/2/14 9:32
 * @Describe: 数据统计返回对象
 */
@Data
public class DataStatisticsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 作物*/
    private String cropName;

    /** 品种*/
    private String breeds;

    /** 可采收数量*/
    private Integer num;

    /** 所属区域*/
    private String region;

    /** 所属大棚*/
    private String houseName;

    /** 预计采收时间*/
    private Date harvestDate;
}
