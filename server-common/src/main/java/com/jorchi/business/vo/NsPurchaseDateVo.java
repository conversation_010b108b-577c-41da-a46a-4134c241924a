package com.jorchi.business.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * NsPurchaseDateVo对象。对应实体描述：历史采收
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsPurchaseDateVo {

    /** 采收日期 */
    private Date harvestDate;
    /** 作物名称 */
    private String cropName;
    /** 品种 */
    private String breeds;
    /** 单位面积产量 */
    private String actualOutput;
    /** 总产量 */
    private String actualTotalOutput;
}
