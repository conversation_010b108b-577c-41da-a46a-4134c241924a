package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsStockInVo对象。对应实体描述：入库记录表
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsStockInVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "stock_in_id", description = "入库记录ID")
    private Long stockInId;

    @Excel(name = "business_type", description = "业务类型:1-采购入库")
    private Integer businessType;

    @Excel(name = "purchase_id", description = "采购申请")
    private Long purchaseId;

    @Excel(name = "purchase_code", description = "关联采购单号")
    private String purchaseCode;

    @Excel(name = "warehouse_id", description = "入库仓库")
    private Long warehouseId;

    @Excel(name = "warehouse_code", description = "入库仓库编码")
    private String warehouseCode;

    @Excel(name = "stock_in_date", description = "入库日期")
    private Date stockInDate;

    @Excel(name = "operator", description = "经办人")
    private String operator;

    @Excel(name = "operator_id", description = "经办人ID")
    private Long operatorId;

    @Excel(name = "supplier", description = "供应商")
    private String supplier;

    @Excel(name = "supplier_id", description = "供应商ID")
    private Long supplierId;

    @Excel(name = "input_type", description = "投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他")
    private Integer inputType;

    @Excel(name = "business_id", description = "投入品ID")
    private Long businessId;

    @Excel(name = "input_category", description = "投入品类别")
    private String inputCategory;

    @Excel(name = "input_name", description = "投入品名称")
    private String inputName;

    @Excel(name = "effective_date", description = "有效日期")
    private Date effectiveDate;

    @Excel(name = "production_date", description = "生产日期")
    private Date productionDate;

    @Excel(name = "unit_price", description = "采购单价")
    private BigDecimal unitPrice;

    @Excel(name = "quantity", description = "采购数量")
    private BigDecimal quantity;

    @Excel(name = "stock_quantity", description = "入库数量")
    private BigDecimal stockQuantity;

    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

}
