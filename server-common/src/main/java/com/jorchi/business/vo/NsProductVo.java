package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsProductVo对象。对应实体描述：半成品转成品表
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsProductVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "product_id", description = "入库ID")
    private Long productId;

    @Excel(name = "warehouse_id", description = "入库ID")
    private Long warehouseId;

    @Excel(name = "house_id", description = "来源大棚")
    private Long houseId;

    @Excel(name = "pick_date", description = "采摘日期")
    private Date pickDate;

    @Excel(name = "production_plan_id", description = "生产计划")
    private Long productionPlanId;

    @Excel(name = "production_plan_code", description = "生产计划编号")
    private String productionPlanCode;

    @Excel(name = "actual_quantity", description = "实际入库量")
    private BigDecimal actualQuantity;

    @Excel(name = "semi_product_id", description = "半成品ID")
    private Long semiProductId;

    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "crop_type", description = "农产品类型")
    private String cropType;

    @Excel(name = "crop_name", description = "农产品名称")
    private String cropName;
    // 大棚编号
    @Excel(name = "house_code", description = "大棚编号")
    private String houseName;

    // 仓库编
    @Excel(name = "warehouse_code", description = "仓库编号")
    private String warehouseCode;

    @Excel(name = "items",description = "分装明细")
    private String items;
}
