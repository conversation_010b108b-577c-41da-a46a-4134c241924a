package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
/**
 * NsPlantingVo对象。对应实体描述：大棚分区总数是否在植大棚数
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsPlantingVo {
    /**
     * 大棚区块名称
     */
    private String houseRegionName;
    /**
     * 在植大棚数
     */
    private Integer isPlant;
}
