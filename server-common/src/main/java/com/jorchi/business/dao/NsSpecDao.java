package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.NsSpecForm;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.NsSpec;
import com.jorchi.common.MybatisBaseDao;

/**
 * 表ns_spec对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: 周建宇 at jorchi
 * @date: 2025-06-14 10:50:47
 */
@Mapper
public interface NsSpecDao extends MybatisBaseDao<NsSpec, Long> {

    /** 根据 Vo 查询 */
    Page<NsSpec> selectPage(NsSpec entity);

    /** 根据 Form 查询 */
    Page<NsSpec> selectPageByForm(NsSpecForm entity);


    // 在此添加其它自定义方法 ...
}
