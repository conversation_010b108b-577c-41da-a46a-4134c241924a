package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.NsAuditExecuteForm;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.NsAuditExecute;
import com.jorchi.common.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表ns_audit_execute对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: xubinbin at jorchi
 * @date: 2022-11-17 09:42:32
 */
@Mapper
public interface NsAuditExecuteDao extends MybatisBaseDao<NsAuditExecute, Long> {

    /** 根据 Vo 查询 */
    Page<NsAuditExecute> selectPage(NsAuditExecute entity);

    /** 根据 Form 查询 */
    Page<NsAuditExecute> selectPageByForm(@Param("entity") NsAuditExecuteForm entity);


    /**根据用户id查找下发层级*/
    String findPersonLevel(Long userId);

    /** 历史稽核查找（小程序）*/
    List<NsAuditExecute> selectHistoryAudit(@Param("houseName") String name, @Param("userId") Long userId);

    /**今日新增-稽核任务查询（小程序）*/
    List<NsAuditExecute> getTodayList(@Param("userId")Long userId,@Param("houseName") String name);

    /**根据大棚编号查找技术人员ID*/
    Long findTechnicalPersonnelId(String houseName);

    /**逾期稽核任务查询（小程序）*/
    List<NsAuditExecute> getDelayList(@Param("userId")Long userId, @Param("houseName")String houseName);

    /**根据用户id查找用户*/
    String findTechnical(Long technicalPersonnelId);

    /**今日已处理-稽核任务（小程序）*/
    List<NsAuditExecute> getTodayDetailList(@Param("userId")Long userId, @Param("houseName")String houseName);

    /** 稽核任务处理*/
    void detailTask(@Param("auditId") Long id, @Param("userId") Long userId,@Param("status") int status,@Param("pictureUrl") String pictureUrl);

    /** 小程序端稽核任务详情查看*/
    NsAuditExecute selectById(Long auditId);

    /** 每日检查稽核任务是否逾期*/
    void updateAuditTask();

    /** 查找大棚编号*/
    String selectHouseName(String id);


    // 在此添加其它自定义方法 ...
}
