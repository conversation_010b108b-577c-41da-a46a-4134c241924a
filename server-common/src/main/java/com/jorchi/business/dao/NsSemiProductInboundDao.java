package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.NsSemiProductInboundForm;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.NsSemiProductInbound;
import com.jorchi.common.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表ns_semi_product_inbound对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: 周建宇 at jorchi
 * @date: 2024-12-05 15:02:19
 */
@Mapper
public interface NsSemiProductInboundDao extends MybatisBaseDao<NsSemiProductInbound, Long> {

    /** 根据 Vo 查询 */
    Page<NsSemiProductInbound> selectPage(NsSemiProductInbound entity);

    /** 根据 Form 查询 */
    Page<NsSemiProductInbound> selectPageByForm(NsSemiProductInboundForm entity);



    // 在此添加其它自定义方法 ...
    /** 批量更新为已转换成品 */
    int updateConvertedBatch(@Param("inboundIds") List<Long> ids);
}
