package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.NsTaskCatch;
import com.jorchi.business.form.NsTaskForm;
import com.jorchi.business.po.NsProductionPlan;
import com.jorchi.business.vo.*;
import com.jorchi.server.vo.Harvest;
import com.jorchi.server.vo.HistoricalHarvestVo;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.NsTask;
import com.jorchi.common.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 表ns_task对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: ChenLiFeng at jorchi
 * @date: 2022-11-11 17:39:46
 */
@Mapper
public interface NsTaskDao extends MybatisBaseDao<NsTask, Long>  {

    /** 根据 Vo 查询 */
    Page<NsTask> selectPage(NsTask entity);

    /** 根据 Form 查询 */
    Page<NsTask> selectPageByForm(NsTaskForm entity,@Param("nsProductionPlanId")Long nsProductionPlanId);

    /** 根据 Form (异常任务)查询 */
    List<NsTaskCatchVO> selectPageByCatch(@Param("vo") NsTaskCatch vo);

    /**根据id查看异常任务详情*/
    NsTaskCatchVO getInfo(Long taskId);

    /** 逾期任务查询 */
    List<DelayTaskVo> selectPageByDelay(@Param("vo") NsTaskCatch vo);

    /** 根据 任务主键取消单条任务*/
    void cancelSingle(long taskId);

    /**根据技术人员id，大鹏名称查找所有的任务id*/
    List<Long> selectTaskIds(@Param("artisanId") long artisanId, @Param("houseName") String houseName);

    /**批量取消所有任务*/
    void updateBatch(@Param("list") List<Long> taskIds);

    /**移交任务审核通过*/
    int inspectPass(@Param("transferId") long transferId,@Param("artisanId") long artisanId,@Param("userId")Long userId);

    /**移交任务审核不通过*/
    void inspectRefuse(@Param("transferId") long transferId,@Param("artisanId") long artisanId,@Param("userId")Long userId);

    /**查找人员信息*/
    List<UserVo> findUserInfo(Long roleId);

    /**
     * 根据用户id查询今日任务
     * @param userId
     * @return
     */
    Integer selectTodayNum(Long userId);
    /**
     * 根据用户id查询逾期任务
     * @param userId
     * @return
     */
    Integer selectBeOverdueNum(Long userId);

    /**
     * 根据用户id查询本日完成任务
     * @param userId
     * @return
     */
    Integer selectCompletedTodayNum(Long userId);

    /**
     * 小程序任务列表返回
     * @return
     */
    Page<NsTaskAppVo> selectPageByFormApp( NsTaskForm nsTaskForm);

    /**
     * 小程序根据主键查询任务
     * @param id
     * @return
     */
    NsTaskAppVo selectFindAppById(Long id);

    /**
     * 小程序首页公告任务数量查询
     * @return
     */
    List<NsTaskAppVo> selectNotice();

    /**
     * 获取当前时间归属于哪个任务
     */
    NsTask selectPageByPlanId(@Param("nsProductionPlanId")Long nsProductionPlanId);


    // 在此添加其它自定义方法 ...

    /**
     * 查询完成任务时间最晚的任务
     * @param id
     * @return
     */
    NsTask selectByPlanId(Long id);


    List<NsTask> selectPageByHousrId(Long id);

    /** 判断是否到休假移交时间*/
    Integer selectIsHoliday();

    /** 判断是否是技术人员*/
    int judgeTechnical(Long userId);

    /**逾期任务查询（小程序）*/
    List<DelayTaskVo> getListDelay(@Param("houseName") String name, @Param("userId") Long userId);

    /**查询今日已完成任务列表*/
    List<TodayCompleteVo> getCompletedList(@Param("houseName")String houseName,@Param("userId") Long userId);

    /** 查找大棚编号、种植区域面积、种植面积*/
    HistoricalHarvestVo getHouseInfo(String houseName);

    /** 查找生产计划id*/
    List<Long> selectPlantId(Long houseId);

    /** 查找历史采收详细信息*/
    Harvest getHistoricalHarvestList(long plantId);

    /** 每日更新未完成的采收任务状态*/
    void updateHarvestTask();

    /** 逻辑删除任务*/
    void updateStatus(Long id);

    /** 刷新大棚现在的状态*/
    void updateHouseStatus(Long houseId);

    /** 查找对应的大棚id*/
    Long selectHouseId(Long id);

    /* 查找逾期任务数量**/
    Integer selectDelayNum(Long artisanId);

    /** 查询大棚的流程*/
    List<NsTask> getProgress(Long houseId);

    /**
     * 查看本日新增任务
     * @Author: xubinbin
     * @Date: 2023年7月12日14:48
     */
    Integer selectTodayNumTogether(Long userId);

    /**
     * 查看当日任务
     * @Author: xubinbin
     * @Date: 2023年7月13日11:37
     */
    List<NsTaskAppVo> selectTask(NsTaskForm nsTaskForm);

    /**
     * 获取今日任务量信息
     * @return
     */
    Map<String, Long> getTodayTask(@Param("regionDeptId") Long regionDeptId);

    /**
     * 获取该计划的总任务数和已完成任务数
     * @param id
     * @return
     */
    Map<String, Long> selectPlanTask(Long id);

    /**
     * 根据计划id查找该计划是否还存在未完成的任务
     * @param id
     * @return
     */
    Integer selectTasks(Long id);

    /**
     * 更新计划表的描述信息
     * @param planId
     * @param farmingProcessId
     * @param context
     */
    void updateDescription(@Param("planId") Long planId, @Param("farmingProcessId") Long farmingProcessId, @Param("description") String context);
}
