package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.NsCropBreedsForm;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.NsCropBreeds;
import com.jorchi.common.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 表ns_crop_breeds对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: GeSenQi at jorchi
 * @date: 2022-11-10 11:34:38
 */
@Mapper
public interface NsCropBreedsDao extends MybatisBaseDao<NsCropBreeds, Long> {

    /** 根据 Vo 查询 */
    Page<NsCropBreeds> selectPage(NsCropBreeds entity);

    /** 根据 Form 查询 */
    Page<NsCropBreeds> selectPageByForm(NsCropBreedsForm entity);

    /** 根据entity精确查询 */
    NsCropBreeds uniqueResult(NsCropBreeds nsCropBreeds);

    /** 新增种植标准——作物名称下拉列表 */
    List<String> queryCropNameList(@Param("cropName") String cropName,@Param("regionDeptId") Long regionDeptId);

    /** 新增种植标准——作物下拉二三级列表 */
    List<NsCropBreeds> queryCropsList(NsCropBreeds entity);

    /** 种植标准配置-新建-作物名称下拉列表*/
    List<String> getCropList(@Param("regionDeptId") Long regionDeptId);

    /** 根据作物名称选品种*/
    List<String> getBreedsList(@Param("name") String name,@Param("regionDeptId") Long regionDeptId);

    /**根据品种选种植模式*/
    List<String> getPatternList(@Param("name") String name,@Param("regionDeptId") Long regionDeptId);

    /**查询作物名称下拉列表*/
    List<String> getCropNameList(@Param("regionDeptId") Long regionDeptId);

    /** 返回去重的品种*/
    List<String> getCropBreedsList(@Param("cropName") String cropName,@Param("regionDeptId") Long regionDeptId);

    /** 查找蔬菜的品种和大类*/
    Map<String, Long> selectCount(@Param("regionDeptId") Long regionDeptId);



    // 在此添加其它自定义方法 ...
    /** 根据农产品id查询种子 */
    List<NsCropBreeds> selectByProductStockIds(@Param("ids") List<Long> productStockIds);
}
