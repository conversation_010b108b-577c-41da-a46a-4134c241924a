package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.HistoryDataBarkForm;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.HistoryDataBark;
import com.jorchi.common.MybatisBaseDao;

/**
 * 表history_data_bark对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: xubinbin at jorchi
 * @date: 2023-04-07 13:51:09
 */
@Mapper
public interface HistoryDataBarkDao extends MybatisBaseDao<HistoryDataBark, Long> {

    /** 根据 Vo 查询 */
    Page<HistoryDataBark> selectPage(HistoryDataBark entity);

    /** 根据 Form 查询 */
    Page<HistoryDataBark> selectPageByForm(HistoryDataBarkForm entity);


    // 在此添加其它自定义方法 ...
}
