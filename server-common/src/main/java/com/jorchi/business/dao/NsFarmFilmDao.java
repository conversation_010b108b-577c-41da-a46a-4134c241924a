package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.NsFarmFilmForm;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.NsFarmFilm;
import com.jorchi.common.MybatisBaseDao;

/**
 * 表ns_farm_film对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: 周建宇 at jorchi
 * @date: 2024-12-14 20:13:55
 */
@Mapper
public interface NsFarmFilmDao extends MybatisBaseDao<NsFarmFilm, Long> {

    /** 根据 Vo 查询 */
    Page<NsFarmFilm> selectPage(NsFarmFilm entity);

    /** 根据 Form 查询 */
    Page<NsFarmFilm> selectPageByForm(NsFarmFilmForm entity);


    // 在此添加其它自定义方法 ...
}
