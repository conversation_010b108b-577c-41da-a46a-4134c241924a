package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.NsPlantingMaterialsForm;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.NsPlantingMaterials;
import com.jorchi.common.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表ns_planting_materials对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: GeSenQi at jorchi
 * @date: 2022-11-10 23:46:53
 */
@Mapper
public interface NsPlantingMaterialsDao extends MybatisBaseDao<NsPlantingMaterials, Long> {

    /** 根据 Vo 查询 */
    Page<NsPlantingMaterials> selectPage(NsPlantingMaterials entity);

    /** 根据 Form 查询 */
    Page<NsPlantingMaterials> selectPageByForm(NsPlantingMaterialsForm entity);

    /** 根据 entity 精确查询 */
    NsPlantingMaterials uniqueResult(NsPlantingMaterials entity);

    /** 根据种植项ID查询 */
    List<NsPlantingMaterials> getListByPlantingClauseIdList(@Param("plantingClauseIdList") List<Long> plantingClauseIdList);

    /** 根据种植项ID更新 */
    int updateByPlantingClauseId(NsPlantingMaterials entity);

    /** 统计数量 */
    int countByEntity(NsPlantingMaterials entity);
    // 在此添加其它自定义方法 ...
}
