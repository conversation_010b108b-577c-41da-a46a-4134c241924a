package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_planting_materials的PO对象<br/>
 * 对应表名：ns_planting_materials，备注：种植项物料表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_planting_materials")
public class NsPlantingMaterials implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 种植项物料ID */
    @Excel(name = "planting_materials_id", description = "种植项物料ID")
    private Long plantingMaterialsId;
    /** 种植项ID */
    @Excel(name = "planting_clause_id", description = "种植项ID")
    private Long plantingClauseId;
    /** 品牌名称 */
    @Excel(name = "brand_name", description = "品牌名称")
    private String brandName;
    /** 投入品名称 */
    @Excel(name = "active_ingredient", description = "投入品名称")
    private String activeIngredient;
    /** 占比 */
    @Excel(name = "ratio", description = "占比")
    private BigDecimal ratio;
    /** 使用方法 */
    @Excel(name = "use_method", description = "使用方法")
    private String useMethod;
    /** 备注 */
    @Excel(name = "remark", description = "备注")
    private String remark;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
