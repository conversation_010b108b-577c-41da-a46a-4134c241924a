package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_fertilizer_select的PO对象<br/>
 * 对应表名：ns_fertilizer_select，备注：种植项配置（选择肥料）
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_fertilizer_select")
public class NsFertilizerSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 选择肥料表id */
    @Excel(name = "fertilizer_id", description = "选择肥料表id")
    private Long fertilizerId;
    /** 种植项id */
    @Excel(name = "planting_clause_id", description = "种植项id")
    private Long plantingClauseId;
    /** 肥料ID */
    @Excel(name = "business_id", description = "肥料ID")
    private Long businessId;
    /** 肥料类型 */
    @Excel(name = "fertilizer_type", description = "肥料类型")
    private String fertilizerType;
    /** 产品名称 */
    @Excel(name = "fertilizerName", description = "产品名称")
    private String fertilizerName;
    /** 使用方法 */
    @Excel(name = "useMethod", description = "使用方法")
    private String useMethod;
    /** 使用量（公斤/棚） */
    @Excel(name = "useDosage", description = "使用量（公斤/棚）")
    private String useDosage;
    /** 每平米用量 */
    @Excel(name = "avgDosage", description = "每平米用量")
    private String avgDosage;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
