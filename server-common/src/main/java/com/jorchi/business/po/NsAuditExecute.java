package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_audit_execute的PO对象<br/>
 * 对应表名：ns_audit_execute，备注：稽核执行
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_audit_execute")
public class NsAuditExecute implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 稽核ID */
    @Excel(name = "audit_id", description = "稽核ID")
    private Long auditId;
    /** 稽核总项 */
    @Excel(name = "audit_total", description = "稽核总项")
    private String auditTotal;
    /** 稽核事项 */
    @Excel(name = "audit_name", description = "稽核事项")
    private String auditName;
    /** 备注信息 */
    @Excel(name = "audit_remarks", description = "备注信息")
    private String auditRemarks;
    /** 种植区 */
    @Excel(name = "planting_area", description = "种植区")
    private String plantingArea;
    /** 大棚编号 */
    @Excel(name = "house_name", description = "大棚编号")
    private String houseName;
    /** 执行日期 */
    @Excel(name = "execute_time", description = "执行日期")
    private Date executeTime;
    /** 技术人员名称 */
    @Excel(name = "technical_personnel", description = "技术人员名称")
    private String technicalPersonnel;
    /** 技术人员id */
    @Excel(name = "technical_personnel_id", description = "技术人员id")
    private Long technicalPersonnelId;
    /** 下发层级 */
    @Excel(name = "sending_level", description = "下发层级")
    private String sendingLevel;
    /** 图片 */
    @Excel(name = "picture_url", description = "图片")
    private String pictureUrl;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 是否逾期（0表示未逾期，1表示已逾期） */
    @Excel(name = "delay", description = "是否逾期（0表示未逾期，1表示已逾期）")
    private Integer delay;
    /** 处理状态(0代表未完成1代表已完成) */
    @Excel(name = "current_statu", description = "处理状态(0代表未完成1代表已完成)")
    private Integer currentStatu;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
