package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_purchase_record的PO对象<br/>
 * 对应表名：ns_purchase_record，备注：采购记录表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_purchase_record")
public class NsPurchaseRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 采购记录ID */
    @Excel(name = "purchase_record_id", description = "采购记录ID")
    private Long purchaseRecordId;
    /** 采购ID */
    @Excel(name = "purchase_id", description = "采购ID")
    private Long purchaseId;
    /** 采购单号 */
    @Excel(name = "purchase_code", description = "采购单号")
    private String purchaseCode;
    /** 申请人 */
    @Excel(name = "applicant", description = "申请人")
    private String applicant;
    /** 申请日期 */
    @Excel(name = "application_date", description = "申请日期")
    private Date applicationDate;
    /** 申请人手机号 */
    @Excel(name = "applicant_mobile", description = "申请人手机号")
    private String applicantMobile;
    /** 申请人ID */
    @Excel(name = "applicant_id", description = "申请人ID")
    private Long applicantId;
    /** 申请事由 */
    @Excel(name = "application_reason", description = "申请事由")
    private String applicationReason;
    /** 支付状态:0-未支付，1-已支付 */
    @Excel(name = "payment_status", description = "支付状态:0-未支付，1-已支付")
    private Integer paymentStatus;
    /** 支付日期 */
    @Excel(name = "payment_date", description = "支付日期")
    private Date paymentDate;
    /** 支付金额 */
    @Excel(name = "payment_amount", description = "支付金额")
    private BigDecimal paymentAmount;
    /** 删除标志（0代表存在，2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
