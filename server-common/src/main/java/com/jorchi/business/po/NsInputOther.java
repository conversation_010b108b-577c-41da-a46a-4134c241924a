package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_input_other的PO对象<br/>
 * 对应表名：ns_input_other，备注：其他投入品表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_input_other")
public class NsInputOther implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 农膜主键 */
    @Excel(name = "input_other_id", description = "农膜主键")
    private Long inputOtherId;
    /** 供应商 */
    @Excel(name = "supplier_name", description = "供应商")
    private String supplierName;
    /** 其他类型 */
    @Excel(name = "input_type", description = "其他类型")
    private String inputType;
    /** 其他信息 */
    @Excel(name = "details", description = "其他信息")
    private String details;
    /** 使用方法 */
    @Excel(name = "usage_method", description = "使用方法")
    private String usageMethod;
    /** 其他单位 */
    @Excel(name = "other_unit", description = "其他单位")
    private String otherUnit;
    /** 备注 */
    @Excel(name = "remark", description = "备注")
    private String remark;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 库存预警线 */
    @Excel(name = "stock_warning_line", description = "库存预警线")
    private BigDecimal stockWarningLine;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
