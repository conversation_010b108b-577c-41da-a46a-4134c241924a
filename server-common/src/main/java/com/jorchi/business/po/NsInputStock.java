package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_input_stock的PO对象<br/>
 * 对应表名：ns_input_stock，备注：投入品库存表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_input_stock")
public class NsInputStock implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 投入品库存id */
    @Excel(name = "input_id", description = "投入品库存id")
    private Long inputId;
    /** 业务ID */
    @Excel(name = "business_id", description = "业务ID")
    private Long businessId;
    /** 投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他 */
    @Excel(name = "input_type", description = "投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他")
    private Integer inputType;
    /** 投入品分类 */
    @Excel(name = "input_category", description = "投入品分类")
    private String inputCategory;
    /** 投入品类别 */
    @Excel(name = "input_sub_category", description = "投入品类别")
    private String inputSubCategory;
    /** 投入品名称 */
    @Excel(name = "input_name", description = "投入品名称")
    private String inputName;
    /** 库存预警线 */
    @Excel(name = "stock_warning_line", description = "库存预警线")
    private BigDecimal stockWarningLine;
    /** 库存量 */
    @Excel(name = "stock_quantity", description = "库存量")
    private BigDecimal stockQuantity;
    /** 预警状态 */
    @Excel(name = "warning_status", description = "预警状态")
    private String warningStatus;
    /** 删除标记 */
    @Excel(name = "deleted", description = "删除标记")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
    /** 规格 */
    @Excel(name = "spec", description = "规格")
    private String spec;
}
