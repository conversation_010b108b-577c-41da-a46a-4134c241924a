package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_notice的PO对象<br/>
 * 对应表名：ns_notice，备注：小程序首页公告
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_notice")
public class NsNotice implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Excel(name = "notice_id", description = "主键")
    private Long noticeId;
    /** 技术人员Id */
    @Excel(name = "artisan_id", description = "技术人员Id")
    private Long artisanId;
    /** 技术人员名称 */
    @Excel(name = "artisan_name", description = "技术人员名称")
    private String artisanName;
    /** 公告内容 */
    @Excel(name = "specifics", description = "公告内容")
    private String specifics;
    /** 创建者 */
    @Excel(name = "create_by", description = "创建者")
    private String createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新者 */
    @Excel(name = "update_by", description = "更新者")
    private String updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
