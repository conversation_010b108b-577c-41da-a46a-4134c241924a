package com.jorchi.business.form;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsInputsSelect 请求参数对象。
 * 对应表名：ns_input_select，备注：种植项关联投入品表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsInputSelectForm extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "select_id", description = "主键id")
    private Long selectId;

    @Excel(name = "planting_clause_id", description = "种植项id")
    private Long plantingClauseId;

    @Excel(name = "type", description = "投入品类型:1=农膜，2=其他")
    private Integer type;

    @Excel(name = "inputs_type", description = "投入品分类")
    private String inputsType;

    @Excel(name = "inputs_name", description = "投入品名称")
    private String inputsName;

    @Excel(name = "avg_dosage", description = "每平米使用量")
    private BigDecimal avgDosage;

    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "create_time_from", description = "创建时间-查询条件起始时间")
    private Date createTimeFrom;

    @Excel(name = "create_time_to", description = "创建时间-查询条件结束时间")
    private Date createTimeTo;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "update_time_from", description = "更新时间-查询条件起始时间")
    private Date updateTimeFrom;

    @Excel(name = "update_time_to", description = "更新时间-查询条件结束时间")
    private Date updateTimeTo;

}
