package com.jorchi.business.form;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsFarmingProcess 请求参数对象。
 * 对应表名：ns_farming_process，备注：农事流程
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsFarmingProcessForm extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Excel(name = "process_name", description = "农事流程名称")
    private String processName;

    @Excel(name = "input_type", description = "投入品需求1-无需物料2-投入肥料3-投入物料")
    private Integer inputType;

    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
