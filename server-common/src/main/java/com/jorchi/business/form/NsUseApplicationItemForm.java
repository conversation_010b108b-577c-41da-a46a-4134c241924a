package com.jorchi.business.form;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsUseApplicationItem 请求参数对象。
 * 对应表名：ns_use_application_item，备注：领用申请子项表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsUseApplicationItemForm extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "item_id", description = "子项ID")
    private Long itemId;

    @Excel(name = "use_id", description = "采购单号")
    private Long useId;

    @Excel(name = "input_type", description = "投入品大类")
    private Integer inputType;

    @Excel(name = "input_category", description = "投入品子类")
    private String inputCategory;

    @Excel(name = "business_id", description = "商品ID")
    private Long businessId;

    @Excel(name = "input_name", description = "投入品名称")
    private String inputName;

    @Excel(name = "quantity", description = "领用数量")
    private BigDecimal quantity;

    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "create_time_from", description = "创建时间-查询条件起始时间")
    private Date createTimeFrom;

    @Excel(name = "create_time_to", description = "创建时间-查询条件结束时间")
    private Date createTimeTo;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "update_time_from", description = "更新时间-查询条件起始时间")
    private Date updateTimeFrom;

    @Excel(name = "update_time_to", description = "更新时间-查询条件结束时间")
    private Date updateTimeTo;

}
