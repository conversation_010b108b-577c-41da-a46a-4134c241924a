package com.jorchi.business.form;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsSalesOrderReturn 请求参数对象。
 * 对应表名：ns_sales_order_return，备注：销售退库表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsSalesOrderReturnForm extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "return_id", description = "退货id")
    private Long returnId;

    @Excel(name = "applicant", description = "申请人")
    private String applicant;

    @Excel(name = "return_date", description = "退货日期")
    private Date returnDate;

    @Excel(name = "return_date_from", description = "退货日期-查询条件起始时间")
    private Date returnDateFrom;

    @Excel(name = "return_date_to", description = "退货日期-查询条件结束时间")
    private Date returnDateTo;

    @Excel(name = "applicant_mobile", description = "申请人手机号")
    private String applicantMobile;

    @Excel(name = "applicant_id", description = "申请人ID")
    private Long applicantId;

    @Excel(name = "return_reason", description = "退货事由")
    private String returnReason;

    @Excel(name = "return_status", description = "退货状态:0-未发起，1-待审批，2-已驳回，3-已完结")
    private Integer returnStatus;

    @Excel(name = "approved_by", description = "审批人")
    private Long approvedBy;

    @Excel(name = "approved_time", description = "审批时间")
    private Date approvedTime;

    @Excel(name = "approved_time_from", description = "审批时间-查询条件起始时间")
    private Date approvedTimeFrom;

    @Excel(name = "approved_time_to", description = "审批时间-查询条件结束时间")
    private Date approvedTimeTo;

    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "create_time_from", description = "创建时间-查询条件起始时间")
    private Date createTimeFrom;

    @Excel(name = "create_time_to", description = "创建时间-查询条件结束时间")
    private Date createTimeTo;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "update_time_from", description = "更新时间-查询条件起始时间")
    private Date updateTimeFrom;

    @Excel(name = "update_time_to", description = "更新时间-查询条件结束时间")
    private Date updateTimeTo;

    @Excel(name = "order_id", description = "销售订单id")
    private Long orderId;

    @Excel(name = "sales_order_code", description = "销售单号")
    private String salesOrderCode;

    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;

}
