package com.jorchi.business.form;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsPurchaseApplicationItem 请求参数对象。
 * 对应表名：ns_purchase_application_item，备注：采购申请子项表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsPurchaseApplicationItemForm extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "item_id", description = "子项ID")
    private Long itemId;

    @Excel(name = "purchase_id", description = "采购单号")
    private Long purchaseId;

    @Excel(name = "supplier_id", description = "供应商ID")
    private Long supplierId;

    @Excel(name = "supplier_name", description = "供应商名称")
    private String supplierName;

    @Excel(name = "input_type", description = "投入品大类")
    private Integer inputType;

    @Excel(name = "input_category", description = "投入品子类")
    private String inputCategory;

    @Excel(name = "business_id", description = "商品ID")
    private Long businessId;

    @Excel(name = "input_name", description = "采购名称")
    private String inputName;

    @Excel(name = "production_date", description = "生产日期")
    private Date productionDate;

    @Excel(name = "production_date_from", description = "生产日期-查询条件起始时间")
    private Date productionDateFrom;

    @Excel(name = "production_date_to", description = "生产日期-查询条件结束时间")
    private Date productionDateTo;

    @Excel(name = "unit_price", description = "采购单价")
    private BigDecimal unitPrice;

    @Excel(name = "unit", description = "采购单位")
    private String unit;

    @Excel(name = "quantity", description = "数量")
    private Integer quantity;

    @Excel(name = "subtotal", description = "小计（unit_price*quantity）")
    private BigDecimal subtotal;

    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "create_time_from", description = "创建时间-查询条件起始时间")
    private Date createTimeFrom;

    @Excel(name = "create_time_to", description = "创建时间-查询条件结束时间")
    private Date createTimeTo;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "update_time_from", description = "更新时间-查询条件起始时间")
    private Date updateTimeFrom;

    @Excel(name = "update_time_to", description = "更新时间-查询条件结束时间")
    private Date updateTimeTo;

}
