package com.jorchi.business.form;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsProduct 请求参数对象。
 * 对应表名：ns_product，备注：半成品转成品表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsProductForm extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "product_id", description = "入库ID")
    private Long productId;

    @Excel(name = "warehouse_id", description = "入库ID")
    private Long warehouseId;

    @Excel(name = "house_id", description = "来源大棚")
    private Long houseId;

    @Excel(name = "pick_date", description = "采摘日期")
    private Date pickDate;

    @Excel(name = "pick_date_from", description = "采摘日期-查询条件起始时间")
    private Date pickDateFrom;

    @Excel(name = "pick_date_to", description = "采摘日期-查询条件结束时间")
    private Date pickDateTo;

    @Excel(name = "production_plan_id", description = "生产计划")
    private Long productionPlanId;

    @Excel(name = "production_plan_code", description = "生产计划编号")
    private String productionPlanCode;

    @Excel(name = "actual_quantity", description = "实际入库量")
    private BigDecimal actualQuantity;

    @Excel(name = "semi_product_id", description = "半成品ID")
    private Long semiProductId;

    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "create_time_from", description = "创建时间-查询条件起始时间")
    private Date createTimeFrom;

    @Excel(name = "create_time_to", description = "创建时间-查询条件结束时间")
    private Date createTimeTo;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "update_time_from", description = "更新时间-查询条件起始时间")
    private Date updateTimeFrom;

    @Excel(name = "update_time_to", description = "更新时间-查询条件结束时间")
    private Date updateTimeTo;

    @Excel(name = "crop_type", description = "农产品类型")
    private String cropType;

    @Excel(name = "crop_name", description = "农产品名称")
    private String cropName;
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
