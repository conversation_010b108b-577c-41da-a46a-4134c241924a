package com.jorchi.project.image.dao;

import com.jorchi.project.image.po.TImage;
import com.jorchi.project.image.po.TImageTmp;
import org.apache.ibatis.annotations.Mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 *
 * 表ns_t_image对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface TImageDao /*extends MybatisBaseDao<TImage, String>*/ {
    List<TImageTmp> selectOverDue(@Param("expiredTime") Date expiredTime);
    int deleteByPrimaryKey(String id);
    TImage selectByPrimaryKey(String id);

    List<TImage> selectByPrimaryKeys(List<String> ids);
    int updateSelectiveByPrimaryKey(TImageTmp entity);
    int insert(TImage entity);
    int insertSelective(TImage entity);
}
