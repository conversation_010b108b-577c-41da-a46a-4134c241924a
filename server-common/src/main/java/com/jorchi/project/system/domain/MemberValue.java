package com.jorchi.project.system.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 通过反射处理 excel 首行表头与实体类对字段的对应关系
 * <AUTHOR> Sugar.Tan
 * @date : 2022-08-07 13:12
 */
@Data
public class MemberValue {
    /** 字段名称，即实体的字段名 */
    private String name;
    /** 字段描述，即 Excel 导入时的表头 */
    private String description;
    /** 在第几列（从0开始） */
    private int index;

    public MemberValue() {}
    public MemberValue(String name, String description, int index) {
        this.name = name;
        this.description = description;
        this.index = index;
    }
}
