package com.jorchi.project.system.vo;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

import com.jorchi.project.system.domain.SysUser;
// // import io.swagger.annotations.ApiModel;
// // import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * SysUserVo对象.对应实体描述：用户信息表；查询对象
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @ApiModel("SysUserVo对象")
public class SysUserVo extends SysUser implements Serializable {

	// @ApiModelProperty("角色ID（查询条件）")
	private Long roleId;

	// @ApiModelProperty("角色名称")
	private String roleName;
}
