package com.jorchi.project.system.vo;

import com.jorchi.framework.web.domain.BaseEntity;
// // import io.swagger.annotations.ApiModel;
// // import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * SysUserVoH5 维修人员 h5 端 vo
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @ApiModel("SysUserVoH5 维修人员vo( h5 端 )")
public class SysUserVoH5 extends BaseEntity /*extends SysUser*/ implements Serializable {

	// @ApiModelProperty("服务商ID")
	private Long serviceId;

	// @ApiModelProperty("用户ID")
	private Long userId;

	// @ApiModelProperty("用户名称")
	private String userName;

	// @ApiModelProperty("手机号")
	private String phonenumber;

	// @ApiModelProperty("身份证号")
	private String idcard;

	// @ApiModelProperty("部门ID")
	private Long deptId;

	// @ApiModelProperty("状态")
	private Long status;

	public Long getServiceId() {
		return serviceId;
	}

	public void setServiceId(Long serviceId) {
		this.serviceId = serviceId;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPhonenumber() {
		return phonenumber;
	}

	public void setPhonenumber(String phonenumber) {
		this.phonenumber = phonenumber;
	}

	public String getIdcard() {
		return idcard;
	}

	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}
}
