package com.jorchi.project.system.service;

import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.DateUtils;
import com.jorchi.project.system.dao.MaxIdDao;
import com.jorchi.project.system.domain.MaxId;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-02-25
 */
@Service
public class MaxIdServiceImpl /*implements IMaxIdService*/
{
    @Resource
    private MaxIdDao maxIdDao;

    /**
     * 获取最大的ID，并使表中的 MaxValue 增加1
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public long getAndIncrement(String tableName) {
        return getAndIncrement(tableName, false);
    }

    /**
     * 获取最大的ID，并使表中的 MaxValue 增加1 （仅当天有效，第2天又从1开始自境）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public long getAndIncrement(String tableName, boolean byToday) {
        // 必传参数检查
        CommonUtil.assertTrue(tableName != null && !tableName.trim().isEmpty(), "idType can not be empty.");

        MaxId pre_ = maxIdDao.selectMaxIdById(tableName); // sql 语句使用了行级别锁
        if (pre_==null) { // 此 idType 没有最大值
            MaxId maxId = new MaxId();
            maxId.setIdType(tableName);
            maxId.setMaxValue(2049L);
            try {
                maxIdDao.insertMaxId(maxId);	// 此 idType 插入一行记录
                return 2048; // 预留一些给系统使用
            } catch (Exception e) { // 因为 idType 是主键，如果此 idType 已被其它线程插入，那么当前线程就会抛出异常
                pre_ = maxIdDao.selectMaxIdById(tableName); // 使用行级别锁，重新读取已存在的记录
                if (pre_ == null)
                    throw e;
            }
        }

        if (byToday) {
            checkIsToday(pre_);
        }
        CommonUtil.assertTrue(pre_ != null, "selectByPrimaryKey by next value, but pre value is null error!");
        long pre_maxValue = pre_.getMaxValue(); 	// 当前可用的最大值
        pre_.setMaxValue(pre_maxValue+1); 			// 下一个可使用的最大值加1
        maxIdDao.updateMaxId(pre_); // 更新
        return pre_maxValue;
    }

    /** 检查是否是当天，如果当天则不用处理；如果不是当天则把值（当前可用最大值）置为1 */
    private void checkIsToday(MaxId maxId) {
        // not today
        if (maxId.getExpiryDate() == null || maxId.getExpiryDate().getTime() < System.currentTimeMillis()) {
            maxId.setExpiryDate(new Date(DateUtils.getTodayStart().getTime() + CommonDef.ONE_DAY));
            maxId.setMaxValue(1L);
        }
    }
}
