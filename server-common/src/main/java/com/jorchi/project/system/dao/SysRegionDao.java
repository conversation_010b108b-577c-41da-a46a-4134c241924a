package com.jorchi.project.system.dao;

import org.apache.ibatis.annotations.Mapper;

import com.jorchi.project.system.po.SysRegion;
import com.jorchi.common.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 表sys_region对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface SysRegionDao extends MybatisBaseDao<SysRegion, Long> {

    /**
     * 省市区查询
     *     省：parent_id = 100
     *     市：parent_id = 省code
     *     区： parent_id = 市code
     */
    List<SysRegion> selectRegionByParentId(@Param("parentId") long parentId);
}
