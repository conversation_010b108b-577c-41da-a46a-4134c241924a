package com.jorchi.autoupdate.service;

import com.jorchi.common.constant.CommonDef;
import com.jorchi.project.system.dao.SystemVarDao;
import com.jorchi.project.system.domain.SystemVar;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;
import pdfc.framework.web.ApiResponse;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统变量 -- 服务类
 * @author: Sugar.Tan
 * @date: 2021.07.05 14:47
 */
@Service
@Slf4j
public class SystemVarService {

    @Resource
    SystemVarDao systemVarDao;

    private static Map<Integer, SystemVar> NAME_2_VAR = new HashMap();

    /**
     * 主键查询
     */
    public SystemVar getSystemVar(int id) {
        try {
            return systemVarDao.selectByPrimaryKey(id);
        } catch (Throwable t) {
            log.warn(new StringBuilder().append("getSystemVar error! id:").append(id).toString());
            return null;
        }
    }

    /**
     * 新创建一条记录
     */
    public int addVersion(int id, String name, String value)
    {
        SystemVar systemVar = new SystemVar();
        systemVar.setId(id);
        systemVar.setName(name);
        systemVar.setValue(value);
        systemVar.setUpdateTime(new Date());
        return systemVarDao.insertSelective(systemVar);
    }

    /**
     * 更新一个 key 的版本号
     */
    public void setVersion(int id, int version) {
        SystemVar systemVar = new SystemVar();
        systemVar.setId(id);
        systemVar.setUpdateTime(new Date());
        systemVar.setVersion(version);
        systemVarDao.updateSelectiveByPrimaryKey(systemVar);
    }

    /**
     * 更新一个 key 的值
     */
    public void setValue(int id, String value) {
        SystemVar systemVar = new SystemVar();
        systemVar.setId(id);
        systemVar.setUpdateTime(new Date());
        systemVar.setValue(value);
        systemVarDao.updateSelectiveByPrimaryKey(systemVar);
    }


    /**
     * 每秒自动从数据库加载
     * @return 数据库一秒内的数据
     */
    public synchronized SystemVar getFromCache(Integer id) {
        SystemVar cache = NAME_2_VAR.get(id);
        // 缓存存在，且未失效
        if (cache != null && cache.getUpdateTime().getTime() > System.currentTimeMillis())
            return cache;

        // read from db, and set updateTime
        //cache = systemVarDao.selectByName(name);
        cache = systemVarDao.selectByPrimaryKey(id);
        if (cache == null) {
            log.error(new StringBuilder().append("system var not fount, name:").append(id).toString());
            return null;
        }

        // 设置失效时间
        cache.setUpdateTime(new Date(System.currentTimeMillis() + 1000));
        NAME_2_VAR.put(id, cache);
        return cache;
    }

    /**
     * 查询系统的版本信息
     * @return
     */
    public ApiResponse getSystemVarInfo(){

        JSONObject result = new JSONObject();

        // 后端版本
        result.put("codeVersion", CommonDef.SERVER_VERSION);

        // DB Table Version， 数据库版本(table_change.sql的版本)
        SystemVar dbScript = getFromCache(CommonDef.DB_SCRIPT_VERSION);
        if (dbScript != null){
            result.put("dbScriptVersion", dbScript.getValue());
        }

        // dbName, 数据库环境名称(dbName，指的是xx系统开发环境，xx系统测试环境，xx系统生产环境)
        SystemVar profile = getFromCache(CommonDef.DB_NAME_ID);
        if (profile != null){
            result.put("profileName", profile.getValue());
        }

        return ApiResponse.ok(result);
    }
}
