package com.jorchi.autoupdate.task;

import com.jorchi.common.constant.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 用途数据库自动更新<br>
 * 每次重启时，检查数据库版本是否为最新，如果不是，则要更新.<br>
 */
@Configuration
@Slf4j
public class DBAutoUpdate
{
	@Autowired
	private DBTableUpdate dbTableUpdate;

	@Value("")
	String key;

	/**
	 * 系统启动之前执行
	 */
	@PostConstruct
	public boolean checkUpDate()
	{
		if(!dbTableUpdate.checkUpDate())
			return false;
		/*if(!new DBRoutineUpdate().checkUpDate())
			return false;*/
		log.info("DBAutoUpdate finish");
		return true;
	}
}
