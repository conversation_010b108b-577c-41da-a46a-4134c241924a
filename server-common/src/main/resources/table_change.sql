-- 1、mysql linux 环境区分大小写，规定统一使用小写！
-- 2、因生成 run api 时，如果有空格会截断，规定表字段注释禁止使用空格！
-- 3、尽量避免使用 to_date 等数据库迁移不兼容的函数
-- 4、本系统表前缀为 ns_
-- 5、每批 SQL 语句之前必须包含以下格式的两行注释！
-- add by Sugar.Tan, 2021.07.05
/* version: 1.0.1 start */
delete from system_var where id = -1;
delete from system_var where id = -2;
delete from system_var where id = -3;


-- add by Sugar.Tan, 2022.11.08
/* version: 1.0.5 start */
update `max_id` set max_value = max_value + 2000;

-- add by XuBinBin, 2022.11.11
/* version: 1.0.6 start */
create table ns_audit_maintain
(
   audit_id             bigint(20) not null comment '稽核ID',
   audit_total          varchar(128) not null comment '稽核总项',
   audit_name           varchar(128) not null comment '稽核事项',
   audit_description    varchar(128) not null comment '描述信息',
   deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (audit_id)
);
alter table ns_audit_maintain comment '稽核维护';

-- add by GeSenQi, 2022.11.11
/* version: 1.0.7 start */
create table ns_agricultural_materials
(
    agricultural_materials_id bigint(20) not null comment '农资主键',
    brand_name           varchar(100) comment '厂牌名称（公司名称）',
    active_ingredient    varchar(200) comment '有效成分',
    registration_no      varchar(50) comment '登记证号',
    dosage_form          varchar(200) comment '含量剂型',
    pack_quantity        decimal(20,2) comment '包装量',
    pack_unit            varchar(20) comment '包装单位',
    stock                decimal(20,2) comment '库存量',
    stock_unit           varchar(20) comment '库存单位',
    stock_weight         decimal(20,2) comment '库存重量',
    validity             datetime comment '有效期',
    deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (agricultural_materials_id)
);
alter table ns_agricultural_materials comment '农资管理';

create table ns_prevention
(
    prevention_id        bigint(20) not null comment '防治对象主键',
    agricultural_materials_id bigint(20) comment '农资主键',
    prevention_name      varchar(100) comment '防治对象',
    dosage_min           decimal(20,2) comment '最小用药量',
    dosage_max           decimal(20,2) comment '最大用药量',
    dosage_unit          varchar(20) comment '用药单位',
    remark               varchar(250) comment '备注',
    use_method           text comment '使用方法',
    deleted              tinyint(2) comment '删除标志（0代表存在 2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (prevention_id)
);
alter table ns_prevention comment '防治对象表';

create table ns_crop_breeds
(
    crop_id              bigint(20) not null comment '作物主键',
    corp_name            varchar(100) comment '作物名称',
    breeds               varchar(100) comment '品种',
    supplier_name        varchar(100) comment '供应商名称',
    cropping_pattern     varchar(100) comment '种植模式',
    remark               text comment '备注',
    deleted              tinyint(2) comment '删除标志',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (crop_id)
);
alter table ns_crop_breeds comment '作物品种';

create table ns_fertilizer
(
    fertilizer_id        bigint(20) not null comment '肥料主键',
    supplier_name        varchar(200) comment '供应商',
    details              text comment '肥料信息',
    fertilizer_unit      varchar(100) comment '肥料单位',
    remark               text comment '备注',
    deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (fertilizer_id)
);
alter table ns_fertilizer comment '肥料管理';

create table ns_planting_standard
(
    planting_standard_id bigint(20) not null comment '种植标准主键',
    standard_name        varchar(128) comment '种植标准名称',
    corp_name            varchar(100) comment '作物名称',
    breeds               varchar(100) comment '品种',
    cropping_pattern     varchar(100) comment '种植模式',
    deleted              tinyint(2) comment '删除标志',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (planting_standard_id)
);
alter table ns_planting_standard comment '种植标准';

create table ns_planting_clause
(
    planting_clause_id   bigint(20) comment '种植项ID',
    planting_standard_id bigint(20) comment '种植标准ID',
    farming_process_id   bigint(20) comment '农事流程ID',
    farming_process_name varchar(255) comment '农事流程名称',
    input_time           decimal(20,2) comment '作业时间',
    is_repeat            tinyint(2) comment '是否重复执行',
    cycle_start          int comment '周期开始',
    cycle_end            int comment '周期结束',
    is_select            tinyint(2) comment '是否选择物料',
    context              text comment '内容详情',
    deleted              tinyint(2) comment '删除标志',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间'
);
alter table ns_planting_clause comment '种植项';

create table ns_planting_materials
(
    planting_materials_id bigint(20) comment '种植项物料ID',
    planting_clause_id   bigint(20) comment '种植项ID',
    brand_name           varchar(100) comment '品牌名称',
    active_ingredient    varchar(200) comment '投入品名称',
    use_method           text comment '使用方法',
    remark               varchar(250) comment '备注',
    deleted              tinyint(2) comment '删除标志（0代表存在 2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间'
);
alter table ns_planting_materials comment '种植项物料表';

-- add by XuBinBin, 2022.11.17
/* version: 1.0.8 start */
create table ns_audit_execute
(
   audit_id             bigint(20) not null comment '稽核ID',
   audit_total          varchar(128) not null comment '稽核总项',
   audit_name           varchar(128) not null comment '稽核事项',
   audit_remarks        varchar(128) not null comment '备注信息',
   planting_area        varchar(128) comment '种植区',
   house_name           varchar(32) comment '大棚编号',
   execute_time         datetime comment '执行日期',
   technical_personnel  varchar(32) comment '技术人员',
   sending_level        varchar(32) comment '下发层级',
   deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
   current_statu        tinyint(2) comment '处理状态(0代表未完成1代表已完成)',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (audit_id)
);

alter table ns_audit_execute comment '稽核执行';

create table ns_person_union
(
   union_id             bigint(20) not null comment '联合表主键',
   technical_id         bigint(20) not null comment '技术人员id',
   agent_id             bigint(20) not null comment '代理人员id',
   deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
   task_id              bigint(20) not null comment '任务ID',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (union_id)
);

alter table ns_person_union comment '技术人员、代理人员关联表';


-- add by XuBinBin, 2022.11.17
/* version: 1.0.9 start */
alter table ns_person_union
add column start_time datetime null comment '请假开始时间' after `task_id`,
add column stop_time datetime null comment '请假结束时间' after `start_time`;

-- add by XuBinBin, 2022.12.09
/* version: 1.0.10 start */
ALTER TABLE `ns_audit_execute`
ADD COLUMN `technical_personnel_id` bigint(20) NULL COMMENT '技术人员id' AFTER `execute_time`,
MODIFY COLUMN `technical_personnel` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '技术人员名称' AFTER `execute_time`;

-- add by XuBinBin, 2022.12.13
/* version: 1.0.11 start */
ALTER TABLE `ns_audit_execute`
ADD COLUMN `delay` tinyint(2) NULL COMMENT '是否逾期（0表示未逾期，1表示已逾期）' AFTER `deleted`;

-- add by XuBinBin, 2022.12.16
/* version: 1.0.12 start */
ALTER TABLE `ns_agricultural_materials`
ADD COLUMN `usage` varchar(200) NULL COMMENT '使用方法' AFTER `active_ingredient`;

-- add by XuBinBin, 2022.12.19
/* version: 1.0.13 start */
ALTER TABLE `ns_fertilizer`
ADD COLUMN `usage_method` varchar(255) NULL COMMENT '使用量与方法' AFTER `details`;

-- add by XuBinBin, 2022.12.19
/* version: 1.0.14 start */
ALTER TABLE `ns_agricultural_materials`
CHANGE COLUMN `usage` `usage_method` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用方法' AFTER `active_ingredient`;


-- add by XuBinBin, 2022.12.21
/* version: 1.0.15 start */
create table ns_house_temperature_collect_data_history
(
   temperature_id       bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_namet          varchar(200) comment '大棚编号',
   temperature          decimal(20,1) comment '当前温度',
   humidity             decimal(20,1) comment '当前湿度',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (temperature_id)
);

alter table ns_house_temperature_collect_data_history comment '大棚温度传感器采集数据(历史数据)';


create table ns_house_temperature_collect_data
(
   temperature_id       bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_namet          varchar(200) comment '大棚编号',
   temperature          decimal(20,1) comment '当前温度',
   humidity             decimal(20,1) comment '当前温度',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (temperature_id)
);

alter table ns_house_temperature_collect_data comment '大棚温度传感器采集数据';


create table ns_house_temperature_collect_hour_data
(
   temperature_id       bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_namet          varchar(200) comment '大棚编号',
   hour_temperature     decimal(20,1) comment '当前小时积温',
   hour_humidity        decimal(20,1) comment '当前小时平均湿度',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (temperature_id)
);

alter table ns_house_temperature_collect_hour_data comment '大棚温度传感器采集数据(时表)';

create table ns_house_soil_collect_data_history
(
   soid_id              bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_namet          varchar(200) comment '大棚编号',
   conductance          decimal(20,1) comment '当前土壤电导度',
   moisture             decimal(20,1) comment '土壤水分含量',
   ph                   decimal(20,1) comment '土壤酸碱值',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (soid_id)
);

alter table ns_house_soil_collect_data_history comment '大棚土壤传感器采集数据(历史数据)';

create table ns_house_soil_collect_data
(
   soid_id              bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_namet          varchar(200) comment '大棚编号',
   conductance          decimal(20,1) comment '当前土壤电导度',
   moisture             decimal(20,1) comment '土壤水分含量',
   ph                   decimal(20,1) comment '土壤酸碱值',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (soid_id)
);

alter table ns_house_soil_collect_data comment '大棚土壤传感器采集数据';

create table ns_house_lightl_collect_data_story
(
   light_id             bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_namet          varchar(200) comment '大棚编号',
   light                decimal(20,1) comment '当前积光',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (light_id)
);

alter table ns_house_lightl_collect_data_story comment '大棚光照传感器采集数据(历史数据)';


create table ns_house_lightl_collect_data
(
   light_id             bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_namet          varchar(200) comment '大棚编号',
   light                decimal(20,1) comment '当前积光',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (light_id)
);

alter table ns_house_lightl_collect_data comment '大棚光照传感器采集数据';


create table ns_house_lightl_collect_hour_data
(
   light_id             bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_namet          varchar(200) comment '大棚编号',
   hour_light           decimal(20,1) comment '当前积光',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (light_id)
);

alter table ns_house_lightl_collect_hour_data comment '大棚光照传感器采集数据(时表)';


create table ns_house_statement_data
(
   statement_id         bigint(20) not null comment '报表主键',
   house_name           varchar(20) comment '大棚编号',
   max_temperature      decimal(20,2) comment '最高温',
   min_temperature      decimal(20,1) comment '最低温',
   accumulated_temperature decimal(20,1) comment '积温',
   light                decimal(20,1) comment '积光',
   avg_humidity         decimal(20,1) comment '日均相对湿度',
   avg_conductivity     decimal(20,1) comment '日均土壤导电度',
   avg_moisture         decimal(20,1) comment '日均土壤水分含量',
   avg_ph               decimal(20,1) comment '日均土壤酸碱值',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (statement_id)
);

alter table ns_house_statement_data comment '大棚报表数据';

-- add by XuBinBin, 2022.12.21
/* version: 1.0.16 start */
ALTER TABLE `ns_house_temperature_collect_data_history`
CHANGE COLUMN `house_namet` `house_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大棚编号' AFTER `plant_id`;

ALTER TABLE `ns_house_temperature_collect_data`
CHANGE COLUMN `house_namet` `house_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大棚编号' AFTER `plant_id`;

ALTER TABLE `ns_house_temperature_collect_hour_data`
CHANGE COLUMN `house_namet` `house_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大棚编号' AFTER `plant_id`;

ALTER TABLE `ns_house_soil_collect_data_history`
CHANGE COLUMN `soid_id` `soil_id` bigint(20) NOT NULL COMMENT '数据主键' FIRST,
CHANGE COLUMN `house_namet` `house_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大棚编号' AFTER `plant_id`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`soil_id`) USING BTREE;

ALTER TABLE `ns_house_soil_collect_data`
CHANGE COLUMN `soid_id` `soil_id` bigint(20) NOT NULL COMMENT '数据主键' FIRST,
CHANGE COLUMN `house_namet` `house_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大棚编号' AFTER `plant_id`,
DROP PRIMARY KEY,
ADD PRIMARY KEY (`soil_id`) USING BTREE;

ALTER TABLE `ns_house_light_collect_data`
CHANGE COLUMN `house_namet` `house_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大棚编号' AFTER `plant_id`;

ALTER TABLE `ns_house_light_collect_data_history`
CHANGE COLUMN `house_namet` `house_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大棚编号' AFTER `plant_id`;

ALTER TABLE `ns_house_light_collect_hour_data`
CHANGE COLUMN `house_namet` `house_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大棚编号' AFTER `plant_id`;


-- add by XuBinBin, 2022.12.21
/* version: 1.0.17 start */
ALTER TABLE `ns_audit_execute`
ADD COLUMN `picture_url` varchar(500) NULL COMMENT '图片' AFTER `sending_level`;

-- add by XuBinBin, 2023.1.5
/* version: 1.0.18 start */
ALTER TABLE `ns_house_light_collect_hour_data`
ADD COLUMN `next_time` datetime NULL COMMENT '下次定时任务运行时间' AFTER `hour_light`;

ALTER TABLE `ns_house_temperature_collect_hour_data`
ADD COLUMN `next_time` datetime NULL COMMENT '下次定时任务运行时间' AFTER `hour_humidity`;

create table ns_house_soil_collect_day_data
(
   soil_id              bigint(20) not null comment '数据主键',
   plant_id             bigint(20) comment '大棚id',
   house_name           varchar(200) comment '大棚编号',
   conductance          decimal(20,1) comment '今日平均土壤电导率',
   moisture             decimal(20,1) comment '今日平均土壤水分含量',
   ph                   decimal(20,1) comment '今日平均土壤酸碱值',
   deleted              tinyint(2) comment '删除标志',
   create_by            bigint(20) comment '创建人',
   create_time          datetime comment '创建时间',
   update_by            bigint(20) comment '更新人',
   update_time          datetime comment '更新时间',
   primary key (soil_id)
);

alter table ns_house_soil_collect_day_data comment '大棚土壤传感器采集数据（日表）';

-- add by XuBinBin, 2023.1.6
/* version: 1.0.19 start */
ALTER TABLE `ns_house_soil_collect_day_data`
ADD COLUMN `next_time` datetime NULL COMMENT '下次执行定时任务的时间' AFTER `ph`;

-- add by XuBinBin, 2023.1.9
/* version: 1.0.20 start */
ALTER TABLE `ns_house_soil_collect_data`
ADD COLUMN `humidity` decimal(20) NULL COMMENT '当前土壤相对湿度' AFTER `house_name`;

ALTER TABLE `ns_house_soil_collect_data_history`
ADD COLUMN `humidity` decimal(20) NULL COMMENT '当前土壤相对湿度' AFTER `house_name`;

ALTER TABLE `ns_house_soil_collect_day_data`
ADD COLUMN `humidity` decimal(20) NULL COMMENT '今日平均土壤相对湿度' AFTER `house_name`;

-- add by XuBinBin, 2023.1.16
/* version: 1.0.21 start */
create table ns_pesticide_select
(
    pesticide_id         bigint(20) comment '选择农药表id',
    planting_clause_id   bigint(20) comment '种植项id',
    composition          varchar(50) comment '成分含量剂型',
    suppliers            varchar(50) comment '供应商',
    prevention           varchar(50) comment '防治对象',
    avgDosage            varchar(50) comment '每平米用量',
    deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (pesticide_id)
);

alter table ns_pesticide_select comment '种植项配置（选择农药）';

create table ns_fertilizer_select
(
    fertilizer_id        bigint(20) not null comment '选择肥料表id',
    planting_clause_id   bigint(20) comment '种植项id',
    fertilizerName       varchar(50) comment '产品名称',
    useMethod            varchar(50) comment '使用方法',
    useDosage            varchar(50) comment ' 使用量（公斤/棚）',
    avgDosage            varchar(50) comment '每平米用量',
    deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (fertilizer_id)
);

alter table ns_fertilizer_select comment '种植项配置（选择肥料）';

create table ns_breeds_select
(
    breeds_id            bigint(20) comment '选择种子表id',
    planting_clause_id   bigint(20) comment '种植项id',
    cropName             varchar(50) comment '作物名称',
    breeds               varchar(50) comment '品种',
    dosage               varchar(50) comment ' 使用量',
    deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (breeds_id)
);

alter table ns_breeds_select comment '种植项配置（选择种子）';

-- add by XuBinBin, 2023.1.28
/* version: 1.0.22 start */
ALTER TABLE `ns_production_plan`
ADD COLUMN `origin_plant_name` varchar(20) NULL COMMENT '移栽来源种植区' AFTER `actual_total_output`,
ADD COLUMN `origin_plant_id` bigint NULL COMMENT '移栽来源种植区ID' AFTER `origin_plant_name`,
ADD COLUMN `origin_house_id` bigint NULL COMMENT '移栽来源大棚id' AFTER `origin_plant_id`,
ADD COLUMN `origin_house_name` varchar(20) NULL COMMENT '移栽来源大棚编号' AFTER `origin_house_id`;

-- add by XuBinBin, 2023.1.28
/* version: 1.0.23 start */
ALTER TABLE `ns_person_union`
ADD COLUMN `vacation_transfer_id` bigint(20)  NULL COMMENT '休假移交表主键' AFTER `stop_time`;

-- add by XuBinBin, 2023.1.29
/* version: 1.0.24 start */
ALTER TABLE `ns_farming_process`
ADD COLUMN `process_stage` bigint(20) NULL COMMENT '流程阶段' AFTER `remark`;

-- add by XuBinBin, 2023.1.30
/* version: 1.0.25 start */
ALTER TABLE `ns_house`
ADD COLUMN `process_stage` bigint(20) NULL COMMENT '流程阶段' AFTER `is_plant`;

INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (155, 1, '休耕', '#d9d9d9', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 16:26:43', NULL, '2023-01-29 16:26:43', '休耕状态颜色');
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (156, 2, '备耕', '#f8cbad', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 16:27:34', NULL, '2023-01-29 16:27:34', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (157, 3, '备种', '#bdd7ee', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 16:27:55', NULL, '2023-01-29 16:27:55', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (158, 4, '苗期', '#a9d08e', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 16:31:20', NULL, '2023-01-29 16:31:20', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (159, 5, '种植期', '#548235', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 16:31:39', 'admin', '2023-01-29 16:31:44', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (160, 6, '届收期', '#ffc000', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 16:31:57', NULL, '2023-01-29 16:31:57', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (161, 7, '采收期', '#ffff00', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 16:32:09', NULL, '2023-01-29 16:32:09', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (162, 8, '超期', '#ff0000', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 16:32:21', NULL, '2023-01-29 16:32:21', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (163, 9, '育苗期', '#92d050', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-29 17:21:45', NULL, '2023-01-29 17:21:45', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (164, 10, '空棚', '#808080', 'hourse_color', NULL, NULL, 'n', '0', 'admin', '2023-01-30 09:44:08', 'admin', '2023-01-31 10:51:30', NULL);
INSERT INTO `ns_sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (2065, 1, '移栽种植', 'YZZZ', 'sys_Planting_mode', NULL, NULL, 'n', '0', 'admin', '2023-02-01 14:52:06', 'admin', '2023-02-01 14:52:29', NULL);

-- add by XuBinBin, 2023.2.15
/* version: 1.0.26 start */
create table ns_sys_equipment
(
    id                   bigint(20) not null comment '设备表主键',
    device_name          varchar(50) comment '设备名称',
    device_node_id       varchar(50) comment '设备编号',
    device_type          bigint(20) comment '设备类型',
    collect_date         datetime comment '最近采集时间',
    online_status        tinyint(2) comment '上线状态：0:离线，1：在线 ',
    house_id             bigint(20) comment '所属大棚',
    deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (id)
);

alter table ns_sys_equipment comment '设备表';

-- add by XuBinBin, 2023.2.15
/* version: 1.0.27 start */
ALTER TABLE `ns_sys_equipment`
MODIFY COLUMN `device_type` bigint(20) NULL DEFAULT NULL COMMENT '设备类型id' AFTER `device_node_id`,
ADD COLUMN `device_type_name` varchar(50) NULL COMMENT '设备类型名称' AFTER `device_type`;

-- add by XuBinBin, 2023.2.15
/* version: 1.0.28 start */
ALTER TABLE `ns_sys_equipment`
MODIFY COLUMN `device_type` bigint(20) NULL DEFAULT NULL COMMENT '设备类型id(0 是网关)' AFTER `device_node_id`,
ADD COLUMN `gateway_name` varchar(50) NULL COMMENT '网关名称' AFTER `device_type_name`,
ADD COLUMN `gateway_id` bigint(20) NULL COMMENT '网关id' AFTER `gateway_name`;

-- add by XuBinBin, 2023.2.16
/* version: 1.0.29 start */
ALTER TABLE `ns_sys_equipment`
MODIFY COLUMN `gateway_id` varchar(50) NULL DEFAULT NULL COMMENT '网关id' AFTER `gateway_name`;

-- add by XuBinBin, 2023.2.16
/* version: 1.0.30 start */
ALTER TABLE `ns_house_soil_collect_data`
DROP COLUMN `humidity`,
DROP COLUMN `conductance`,
DROP COLUMN `moisture`,
DROP COLUMN `ph`,
CHANGE COLUMN `plant_id` `house_id` bigint(20) NULL DEFAULT NULL COMMENT '大棚id' AFTER `soil_id`,
ADD COLUMN `n` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点ID' AFTER `house_name`,
ADD COLUMN `d` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采集时间' AFTER `n`,
ADD COLUMN `t` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境传感器和网关检测到的环境温度,只有当节点有该传感器时，这个属性才会被发送' AFTER `d`,
ADD COLUMN `l` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境光照' AFTER `t`,
ADD COLUMN `q` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境传感器和网关检测到的环境湿度,只有当节点有该传感器时，这个属性才会被发送' AFTER `l`,
ADD COLUMN `E` int(12) NULL DEFAULT NULL COMMENT '土壤 EC,只有当节点有该传感器时，这个属性才会被发送' AFTER `q`,
ADD COLUMN `A` int(12) NULL DEFAULT NULL COMMENT '土壤传感器测得的土壤TAO（温度）,只有当节点有该传感器时，这个属性才会被发送' AFTER `E`,
ADD COLUMN `M` int(12) NULL DEFAULT NULL COMMENT '壤传感器测得的土壤MO（湿度）,只有当节点有该传感器时，这个属性才会被发送' AFTER `A`,
ADD COLUMN `ph` decimal(10, 2) NULL DEFAULT NULL COMMENT '土壤PH测量范围：0～14，精度：±0.3' AFTER `M`,
ADD COLUMN `b` int(10) NULL COMMENT '土壤传感器电量' AFTER `ph`;

ALTER TABLE `ns_house_soil_collect_data_history`
DROP COLUMN `humidity`,
DROP COLUMN `conductance`,
DROP COLUMN `moisture`,
DROP COLUMN `ph`,
CHANGE COLUMN `plant_id` `house_id` bigint(20) NULL DEFAULT NULL COMMENT '大棚id' AFTER `soil_id`,
ADD COLUMN `n` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点ID' AFTER `house_name`,
ADD COLUMN `d` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采集时间' AFTER `n`,
ADD COLUMN `t` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境传感器和网关检测到的环境温度,只有当节点有该传感器时，这个属性才会被发送' AFTER `d`,
ADD COLUMN `l` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境光照' AFTER `t`,
ADD COLUMN `q` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境传感器和网关检测到的环境湿度,只有当节点有该传感器时，这个属性才会被发送' AFTER `l`,
ADD COLUMN `E` int(12) NULL DEFAULT NULL COMMENT '土壤 EC,只有当节点有该传感器时，这个属性才会被发送' AFTER `q`,
ADD COLUMN `A` int(12) NULL DEFAULT NULL COMMENT '土壤传感器测得的土壤TAO（温度）,只有当节点有该传感器时，这个属性才会被发送' AFTER `E`,
ADD COLUMN `M` int(12) NULL DEFAULT NULL COMMENT '壤传感器测得的土壤MO（湿度）,只有当节点有该传感器时，这个属性才会被发送' AFTER `A`,
ADD COLUMN `ph` decimal(10, 2) NULL DEFAULT NULL COMMENT '土壤PH测量范围：0～14，精度：±0.3' AFTER `M`,
ADD COLUMN `b` int(10) NULL COMMENT '土壤传感器电量' AFTER `ph`;


ALTER TABLE `ns_house_soil_collect_day_data`
DROP COLUMN `humidity`,
DROP COLUMN `conductance`,
DROP COLUMN `moisture`,
DROP COLUMN `ph`,
CHANGE COLUMN `plant_id` `house_id` bigint(20) NULL DEFAULT NULL COMMENT '大棚id' AFTER `soil_id`,
ADD COLUMN `n` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '节点ID' AFTER `house_name`,
ADD COLUMN `d` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '采集时间' AFTER `n`,
ADD COLUMN `t` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境传感器和网关检测到的环境温度,只有当节点有该传感器时，这个属性才会被发送' AFTER `d`,
ADD COLUMN `l` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境光照' AFTER `t`,
ADD COLUMN `q` decimal(10, 2) NULL DEFAULT NULL COMMENT '环境传感器和网关检测到的环境湿度,只有当节点有该传感器时，这个属性才会被发送' AFTER `l`,
ADD COLUMN `E` int(12) NULL DEFAULT NULL COMMENT '土壤 EC,只有当节点有该传感器时，这个属性才会被发送' AFTER `q`,
ADD COLUMN `A` int(12) NULL DEFAULT NULL COMMENT '土壤传感器测得的土壤TAO（温度）,只有当节点有该传感器时，这个属性才会被发送' AFTER `E`,
ADD COLUMN `M` int(12) NULL DEFAULT NULL COMMENT '壤传感器测得的土壤MO（湿度）,只有当节点有该传感器时，这个属性才会被发送' AFTER `A`,
ADD COLUMN `ph` decimal(10, 2) NULL DEFAULT NULL COMMENT '土壤PH测量范围：0～14，精度：±0.3' AFTER `M`;

-- add by XuBinBin, 2023.2.16
/* version: 1.0.31 start */
ALTER TABLE `ns_sys_equipment`
ADD COLUMN `house_name` varchar(50) NULL COMMENT '所属大棚名称' AFTER `house_id`;


-- add by XuBinBin, 2023.2.22
/* version: 1.0.32 start */
ALTER TABLE `ns_house_soil_collect_day_data`
MODIFY COLUMN `E` decimal(10, 2) NULL DEFAULT NULL COMMENT '土壤 EC,只有当节点有该传感器时，这个属性才会被发送' AFTER `q`,
MODIFY COLUMN `A` decimal(10, 2) NULL DEFAULT NULL COMMENT '土壤传感器测得的土壤TAO（温度）,只有当节点有该传感器时，这个属性才会被发送' AFTER `E`,
MODIFY COLUMN `M` decimal(10, 2) NULL DEFAULT NULL COMMENT '壤传感器测得的土壤MO（湿度）,只有当节点有该传感器时，这个属性才会被发送' AFTER `A`;

-- add by XuBinBin, 2023.3.21
/* version: 1.0.33 start */
ALTER TABLE `ns_breeds_select`
MODIFY COLUMN `cropName` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作物名称' AFTER `planting_clause_id`,
MODIFY COLUMN `breeds` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '品种' AFTER `cropName`,
MODIFY COLUMN `dosage` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT ' 使用量' AFTER `breeds`;
ALTER TABLE `ns_fertilizer_select`
MODIFY COLUMN `fertilizerName` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '产品名称' AFTER `planting_clause_id`,
MODIFY COLUMN `useMethod` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '使用方法' AFTER `fertilizerName`,
MODIFY COLUMN `useDosage` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT ' 使用量（公斤/棚）' AFTER `useMethod`,
MODIFY COLUMN `avgDosage` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '每平米用量' AFTER `useDosage`;
ALTER TABLE `ns_pesticide_select`
MODIFY COLUMN `composition` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '成分含量剂型' AFTER `planting_clause_id`,
MODIFY COLUMN `suppliers` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '供应商' AFTER `composition`,
MODIFY COLUMN `prevention` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '防治对象' AFTER `suppliers`,
MODIFY COLUMN `avgDosage` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '每平米用量' AFTER `prevention`;


-- add by XuBinBin, 2023.4.4
/* version: 1.0.34 start */
ALTER TABLE `ns_planting_clause`
ADD COLUMN `index_num` int(10) NULL COMMENT '序列号' AFTER `planting_clause_id`;
ALTER TABLE `ns_house_soil_collect_data_history`
ADD INDEX `date`(`n`, `d`) USING BTREE,
ADD INDEX `d`(`d`) USING BTREE;

-- add by XuBinBin, 2023.4.5
/* version: 1.0.35 start */
create table history_data_bark
(
    id                   bigint(20) not null comment '备份表主键',
    db_name              varchar(100) comment '备份表名称',
    start_time           datetime comment '备份开始日期',
    stop_time            datetime comment '备份结束日期',
    primary key (id)
);

alter table history_data_bark comment '设备数据历史表备份记录表';

-- add by XuBinBin, 2023.5.11
/* version: 1.0.36 start */
create table ns_prod_audit
(
    id                   bigint(20) not null comment '日常稽核表主键',
    region_id            bigint(20) comment '种植区id',
    region_name          varchar(50) comment '种植区名称',
    house_id             bigint(20) comment '大棚id',
    house_name           varchar(50) comment '大棚编号',
    crop_name            varchar(50) comment '作物名称',
    aspect               varchar(50) comment '外观',
    disease              varchar(50) comment '病害',
    insect_pest          varchar(50) comment '虫害',
    canopy_film          varchar(50) comment '棚膜',
    door                 varchar(50) comment '大门',
    exception_description varchar(50) comment '异常说明',
    deleted              tinyint(2) comment '删除标志（0代表存在2代表删除）',
    create_by            bigint(20) comment '创建人',
    create_time          datetime comment '创建时间',
    update_by            bigint(20) comment '更新人',
    update_time          datetime comment '更新时间',
    primary key (id)
);

alter table ns_prod_audit comment '生产日常稽核表';

-- add by XuBinBin, 2023.8.15
/* version: 1.0.37 start */
ALTER TABLE `ns_production_plan`
ADD COLUMN `complete_flag` tinyint(2) NULL COMMENT '计划周期是否完成(0:表示未完成,1:表示已完成)' AFTER `update_time`;

-- add by XuBinBin, 2023.8.29
/* version: 1.0.38 start */
ALTER TABLE `ns_production_plan`
ADD COLUMN `other_remark` varchar(255) NULL COMMENT '备注' AFTER `complete_flag`;

-- add by XuBinBin, 2023.12.07
/* version: 1.0.39 start */
ALTER TABLE `ns_task`
ADD COLUMN `description` varchar(255) NULL COMMENT '事项说明' AFTER `technician_agent_name`;

-- add by BingHan, 2024.4.08
/* version: 1.0.40 start */
ALTER TABLE ns_region
    ADD COLUMN region_dept_id BIGINT(20) NOT NULL COMMENT '农场ID' AFTER region_name;
ALTER TABLE ns_region
    ADD COLUMN region_dept_name VARCHAR(128) NOT NULL COMMENT '农场名称' AFTER region_dept_id;
ALTER TABLE ns_house
    ADD COLUMN region_dept_id BIGINT(20) NOT NULL COMMENT '农场ID' AFTER house_region_id;
ALTER TABLE ns_house
    ADD COLUMN region_dept_name VARCHAR(128) NOT NULL COMMENT '农场名称' AFTER region_dept_id;

-- add by PanJiaPing, 2024.5.20
/* version: 24.5.20 start */
update ns_region set region_dept_id = 100 where region_dept_id is null;
update ns_house set region_dept_id = 100 where region_dept_id is null;
update ns_region set region_dept_name = '世合' where region_dept_id is null;
update ns_house set region_dept_name = '世合' where region_dept_id is null;
update ns_sys_user set dept_id = 100 where dept_id is null;

-- add by zhoujianyu, 2024.12.13
/* version: 24.5.21.1 start */
create table ns_semi_product_inbound
(
    inbound_id           bigint               not null comment '入库ID'
        primary key,
    warehouse_id         bigint               null comment '入库ID',
    picker               bigint               not null comment '采摘人',
    picker_name          varchar(64)          not null comment '采摘人姓名',
    pick_date            datetime             not null comment '采摘日期',
    house_id             bigint               not null comment '来源大棚',
    production_plan_id   bigint               null comment '生产计划',
    production_plan_code varchar(64)          null comment '生产计划编号',
    actual_quantity      decimal(10, 2)       not null comment '实际入库量',
    converted            tinyint(1) default 0 null comment '是否已转换成品',
    deleted              tinyint(2)           null comment '删除标志',
    create_by            bigint               null comment '创建人',
    create_time          datetime             null comment '创建时间',
    update_by            bigint               null comment '更新人',
    update_time          datetime             null comment '更新时间',
    crop_type            varchar(64)          null comment '农产品类型',
    crop_name            varchar(64)          null comment '农产品名称'
)
    comment '半成品入库表';

/* version: 24.5.21.2 start */
create table ns_product
(
    product_id           bigint         not null comment '入库ID'
        primary key,
    warehouse_id         bigint         null comment '入库ID',
    house_id             bigint         not null comment '来源大棚',
    pick_date            datetime       null comment '采摘日期',
    production_plan_id   bigint         null comment '生产计划',
    production_plan_code varchar(64)    null comment '生产计划编号',
    actual_quantity      decimal(10, 2) not null comment '实际入库量',
    semi_product_id      bigint         not null comment '半成品ID',
    deleted              tinyint(2)     null comment '删除标志',
    create_by            bigint         null comment '创建人',
    create_time          datetime       null comment '创建时间',
    update_by            bigint         null comment '更新人',
    update_time          datetime       null comment '更新时间',
    crop_type            varchar(64)    null comment '农产品类型',
    crop_name            varchar(64)    null comment '农产品名称'
)
    comment '半成品转成品表';
/* version: 24.5.21.3 start */
create table ns_product_scrap
(
    scrap_id           bigint auto_increment comment '报废id'
        primary key,
    applicant          bigint                 not null comment '申请人',
    applicant_name     varchar(64)            not null comment '申请人姓名',
    business_type      int                    not null comment '业务类型，1.半成品入库2.半成品转成品入库3.销售退库',
    warehouse_id       bigint                 null comment '仓库id',
    warehouse_code     varchar(64)            null comment '仓库编号',
    order_id           varchar(64)            null comment '销售订单号',
    production_plan_id bigint                 null comment '生产计划id',
    semi_product_id    bigint                 null comment '半成品入库id',
    product_id         bigint                 null comment '成品id',
    crop_type          varchar(64) default '' null comment '农产品类别',
    crop_name          varchar(64) default '' null comment '农产品名称',
    scrap_quantity     decimal(10, 2)         not null comment '报废数量',
    deleted            int                    null comment '删除标记',
    create_by          bigint                 null comment '创建人',
    create_time        datetime               null comment '创建时间',
    update_by          bigint                 null comment '更新人',
    update_time        datetime               null comment '更新时间'
)
    comment '报废表';
/* version: 24.5.21.4 start */
create table ns_warehouse
(
    warehouse_id      bigint       not null comment '仓库ID'
        primary key,
    category          varchar(20)  not null comment '仓库分类',
    warehouse_code    varchar(64)  not null comment '仓库编号',
    warehouse_name    varchar(255) not null comment '仓库名称',
    location          varchar(255) not null comment '位置',
    stock_quantity    int          not null comment '存量',
    environmental_req varchar(255) not null comment '环境要求',
    facilities        varchar(255) not null comment '设备设施',
    temperature_ctrl  varchar(16)  not null comment '温度控制',
    deleted           tinyint(2)   null comment '删除标志',
    create_by         bigint       null comment '创建人',
    create_time       datetime     null comment '创建时间',
    update_by         bigint       null comment '更新人',
    update_time       datetime     null comment '更新时间'
)
    comment '仓库表';

/* version: 24.5.21.5 start */
alter table ns_crop_breeds add column crop_type varchar(100) comment '作物类型';
/* version: 24.5.21.6 start */
alter table ns_production_plan add column  plan_code varchar(64) comment '生产计划编号';
/* version: 24.5.21.7 start */
UPDATE ns_production_plan SET plan_code = CONCAT(DATE_FORMAT(create_time, '%Y%m%d%H%i%s'), house_id) where plan_code is null;

-- add by zhoujianyu, 2024.12.31
/* version: 24.5.22.1 start */
create table ns_input_stock
(
    input_id           bigint auto_increment comment '投入品库存id'
        primary key,
    business_id        bigint         not null comment '业务ID',
    input_type         int            not null comment '投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他',
    input_category     varchar(64)    not null comment '投入品分类',
    input_sub_category varchar(64)    null comment '投入品类别',
    input_name         varchar(64)    not null comment '投入品名称',
    stock_warning_line decimal(10, 2) null comment '库存预警线',
    stock_quantity     decimal(10, 2) not null comment '库存量',
    warning_status     varchar(64)    not null comment '预警状态',
    deleted            int default 0  not null comment '删除标记',
    create_by          bigint         null comment '创建人',
    create_time        datetime       null comment '创建时间',
    update_by          bigint         null comment '更新人',
    update_time        datetime       null comment '更新时间'
)
    comment '投入品库存表';
/* version: 24.5.22.2 start */
create table ns_farm_film
(
    film_id            bigint         not null comment '农膜主键'
        primary key,
    supplier_name      varchar(200)   null comment '供应商',
    film_type          varchar(32)    null comment '农膜类型',
    details            text           null comment '农膜信息',
    usage_method       varchar(255)   null comment '使用方法',
    film_unit          varchar(100)   null comment '农膜单位',
    remark             text           null comment '备注',
    deleted            tinyint(2)     null comment '删除标志（0代表存在2代表删除）',
    create_by          bigint         null comment '创建人',
    create_time        datetime       null comment '创建时间',
    update_by          bigint         null comment '更新人',
    update_time        datetime       null comment '更新时间',
    stock_warning_line decimal(20, 2) null comment '库存预警线'
)
    comment '农膜管理';

/* version: 24.5.22.3 start */
create table ns_farm_machinery
(
    machinery_id       bigint         not null comment '农机主键'
        primary key,
    supplier_name      varchar(200)   null comment '供应商',
    machinery_type     varchar(32)    null comment '农机类型',
    details            text           null comment '农机信息',
    usage_method       varchar(255)   null comment '使用方法',
    machinery_unit     varchar(100)   null comment '农机单位',
    remark             text           null comment '备注',
    deleted            tinyint(2)     null comment '删除标志（0代表存在2代表删除）',
    create_by          bigint         null comment '创建人',
    create_time        datetime       null comment '创建时间',
    update_by          bigint         null comment '更新人',
    update_time        datetime       null comment '更新时间',
    stock_warning_line decimal(20, 2) null comment '库存预警线'
)
    comment '农机管理';
/* version: 24.5.22.4 start */
create table ns_input_other
(
    input_other_id     bigint         not null comment '农膜主键'
        primary key,
    supplier_name      varchar(200)   null comment '供应商',
    input_type         varchar(32)    null comment '其他类型',
    details            text           null comment '其他信息',
    usage_method       varchar(255)   null comment '使用方法',
    other_unit         varchar(100)   null comment '其他单位',
    remark             text           null comment '备注',
    deleted            tinyint(2)     null comment '删除标志（0代表存在2代表删除）',
    create_by          bigint         null comment '创建人',
    create_time        datetime       null comment '创建时间',
    update_by          bigint         null comment '更新人',
    update_time        datetime       null comment '更新时间',
    stock_warning_line decimal(20, 2) null comment '库存预警线'
)
    comment '其他投入品表';

/* version: 24.5.22.5 start */
alter table ns_fertilizer add column fertilizer_type varchar(32) null comment '肥料类型';
/* version: 24.5.22.6 start */
alter table ns_agricultural_materials add column  agricultural_materials_type varchar(32) null comment '植保剂类型';
/* version: 24.5.22.7 start */
create table ns_product_stock
(
    product_stock_id   bigint auto_increment comment '农产品id'
        primary key,
    crop_type          varchar(64)    not null comment '农产品类别',
    crop_category      varchar(64)    null comment '科别',
    breeds             varchar(64)    null comment '品种',
    crop_name          varchar(64)    not null comment '农产品名称',
    stock_warning_line decimal(10, 2) null comment '库存预警线',
    stock_quantity     decimal(10, 2) null comment '库存量',
    warning_status     varchar(64)    null comment '预警状态',
    deleted            int default 0  not null comment '删除标记',
    create_by          bigint         null comment '创建人',
    create_time        datetime       null comment '创建时间',
    update_by          bigint         null comment '更新人',
    update_time        datetime       null comment '更新时间'
)
    comment '农产品库存表';
/* version: 24.5.22.8 start */
alter table ns_crop_breeds
    add column product_stock_id bigint null comment '农产品分类ID' after crop_id,
    add column crop_category varchar(64) null comment '科别' after crop_name,
    add column stock_warning_line decimal(20, 2) null comment '库存预警线' after update_time;
/* version: 24.5.22.9 start */
create table if not exists ns_input_select
(
    select_id          bigint         not null comment '主键id'
        primary key,
    planting_clause_id bigint         null comment '种植项id',
    business_id        bigint         not null comment '业务ID',
    type               int            null comment '投入品类型:5=农膜，6=其他',
    inputs_type        varchar(64)    null comment '投入品分类',
    inputs_name        varchar(64)    null comment '投入品名称',
    avg_dosage         decimal(10, 2) null comment '每平米使用量',
    deleted            tinyint(2)     null comment '删除标志（0代表存在2代表删除）',
    create_by          bigint         null comment '创建人',
    create_time        datetime       null comment '创建时间',
    update_by          bigint         null comment '更新人',
    update_time        datetime       null comment '更新时间'
)
    comment '种植项关联投入品表';
/* version: 24.5.22.10 start */
alter table ns_fertilizer_select
    add column fertilizer_type varchar(64) null comment '肥料类型' after fertilizer_id,
    add column business_id bigint null comment '业务ID' after fertilizer_type;
/* version: 24.5.22.11 start */
alter table ns_pesticide_select
    add column pesticide_type varchar(64) null comment '植保剂类型' after pesticide_id,
    add column business_id bigint null comment '业务ID' after pesticide_type;
/* version: 24.5.22.12 start */
alter table ns_breeds_select add column business_id bigint null comment '业务ID' after breeds_id;
/* version: 24.5.22.13 start */
create table ns_approval_process
(
    approval_id      bigint auto_increment comment '审批流程ID'
        primary key,
    initiator        varchar(128) not null comment '发起人',
    initiator_id     bigint       not null comment '发起人ID',
    initiation_time  datetime     not null comment '发起时间',
    business_type    varchar(64)  not null comment '业务类型',
    business_id      bigint       not null comment '业务ID',
    approval_content text         not null comment '审批内容',
    approval_status  int          not null comment '审批状态:0-待审批，1-已驳回，2-已完结',
    approver         varchar(128) null comment '审批人',
    approved_by      bigint       null comment '审批人ID',
    approved_time    datetime     null comment '审批时间',
    deleted          tinyint(2)   null comment '删除标志（0代表存在，1代表删除）',
    create_by        bigint       null comment '创建人',
    create_time      datetime     null comment '创建时间',
    update_by        bigint       null comment '更新人',
    update_time      datetime     null comment '更新时间'
)
    comment '审批流程表';
/* version: 24.5.22.14 start */
create table ns_process_task
(
    task_id           bigint auto_increment comment '流程任务ID'
        primary key,
    process_id        bigint        not null comment '流程ID',
    approver          varchar(128)  not null comment '审批人',
    approved_by       bigint        not null comment '审批人ID',
    approval_status   int           not null comment '审批状态:0-待审批，1-已驳回，2-已完结',
    approval_remark   varchar(1024) null comment '审批备注',
    task_sequence     int           not null comment '任务顺序',
    actual_completion datetime      null comment '实际完成时间',
    is_urgent         tinyint(2)    not null comment '是否紧急（0：否，1：是）',
    deleted           tinyint(2)    null comment '删除标志（0代表存在，1代表删除）',
    create_by         bigint        null comment '创建人',
    create_time       datetime      null comment '创建时间',
    update_by         bigint        null comment '更新人',
    update_time       datetime      null comment '更新时间'
)
    comment '流程任务表';
/* version: 24.5.22.15 start */
create table ns_purchase_application
(
    purchase_id        bigint auto_increment comment '采购id'
        primary key,
    purchase_code      varchar(64)   null comment '采购单号',
    applicant          varchar(128)  not null comment '申请人',
    application_date   datetime      not null comment '申请日期',
    applicant_mobile   varchar(32)   null comment '申请人手机号',
    applicant_id       bigint        null comment '申请人ID',
    application_reason varchar(1024) not null comment '申请事由',
    application_status int           not null comment '申请状态:0-未发起，1-待审批，2-已驳回，3-已完结',
    approved_by        bigint        null comment '审批人',
    approved_time      datetime      null comment '审批时间',
    deleted            tinyint(2)    null comment '删除标志（0代表存在，2代表删除）',
    create_by          bigint        null comment '创建人',
    create_time        datetime      null comment '创建时间',
    update_by          bigint        null comment '更新人',
    update_time        datetime      null comment '更新时间'
)
    comment '采购申请表';
/* version: 24.5.22.16 start */
create table ns_purchase_application_item
(
    item_id         bigint auto_increment comment '子项ID'
        primary key,
    purchase_id     bigint         not null comment '采购单号',
    supplier_id     bigint         not null comment '供应商ID',
    supplier_name   varchar(256)   not null comment '供应商名称',
    input_type      int            not null comment '投入品大类',
    input_category  varchar(32)    not null comment '投入品子类',
    business_id     bigint         not null comment '商品ID',
    input_name      varchar(256)   not null comment '采购名称',
    production_date date           null comment '生产日期',
    effective_date  datetime       null comment '有效日期',
    unit_price      decimal(10, 2) not null comment '采购单价',
    unit            varchar(32)    not null comment '采购单位',
    quantity        int            not null comment '数量',
    check_quantity  int            null comment '验收数量',
    check_date      datetime       null comment '验收时间',
    pay_amount      decimal(20, 2) null comment '实付金额',
    subtotal        decimal(10, 2) not null comment '小计（unit_price*quantity）',
    deleted         tinyint(2)     null comment '删除标志（0代表存在，2代表删除）',
    create_by       bigint         null comment '创建人',
    create_time     datetime       null comment '创建时间',
    update_by       bigint         null comment '更新人',
    update_time     datetime       null comment '更新时间'
)
    comment '采购申请子项表';
/* version: ********** start */
create table ns_supplier
(
    supplier_id       bigint auto_increment comment '供应商ID'
        primary key,
    supplier_category varchar(128) not null comment '供应商类别',
    supplier_name     varchar(256) not null comment '供应商名称',
    tax_number        varchar(64)  not null comment '税号',
    address           varchar(255) not null comment '地址',
    bank_name         varchar(128) not null comment '开户银行',
    bank_account      varchar(64)  not null comment '银行账号',
    contact_person    varchar(128) not null comment '联系人',
    contact_phone     varchar(32)  not null comment '联系人电话',
    deleted           tinyint(2)   null comment '删除标志（0代表存在，2代表删除）',
    create_by         bigint       null comment '创建人',
    create_time       datetime     null comment '创建时间',
    update_by         bigint       null comment '更新人',
    update_time       datetime     null comment '更新时间',
    note              varchar(512) null comment '备注'
)
    comment '供应商表';
/* version: ********** start */
create table ns_purchase_record
(
    purchase_record_id bigint auto_increment comment '采购记录ID'
        primary key,
    purchase_id        bigint         not null comment '采购ID',
    purchase_code      varchar(64)    null comment '采购单号',
    applicant          varchar(128)   not null comment '申请人',
    application_date   datetime       not null comment '申请日期',
    applicant_mobile   varchar(32)    null comment '申请人手机号',
    applicant_id       bigint         null comment '申请人ID',
    application_reason varchar(1024)  not null comment '申请事由',
    payment_status     int            not null comment '支付状态:0-未支付，1-已支付',
    payment_date       datetime       null comment '支付日期',
    payment_amount     decimal(10, 2) not null comment '支付金额',
    deleted            tinyint(2)     null comment '删除标志（0代表存在，2代表删除）',
    create_by          bigint         null comment '创建人',
    create_time        datetime       null comment '创建时间',
    update_by          bigint         null comment '更新人',
    update_time        datetime       null comment '更新时间'
)
    comment '采购记录表';
/* version: 24.5.22.19 start */
create table ns_purchase_payment_info
(
    payment_info_id    bigint auto_increment comment '支付信息ID'
        primary key,
    purchase_record_id bigint         not null comment '采购记录ID',
    supplier_id        bigint         not null comment '供应商ID',
    supplier_name      varchar(128)   not null comment '供应商名称',
    supplier_bank      varchar(128)   not null comment '供应商开户行',
    supplier_account   varchar(64)    not null comment '供应商银行账号',
    payment_amount     decimal(10, 2) not null comment '支付金额',
    attachment         varchar(256)   null comment '附件路径',
    deleted            int            null comment '删除标记',
    create_by          bigint         null comment '创建人',
    create_time        datetime       null comment '创建时间',
    update_by          bigint         null comment '更新人',
    update_time        datetime       null comment '更新时间'
)
    comment '采购支付信息表';
/* version: 24.5.22.20 start */
create table ns_return_application
(
    return_id        bigint auto_increment comment '退货id'
        primary key,
    applicant        varchar(128)  not null comment '申请人',
    return_date      datetime      not null comment '退货日期',
    applicant_mobile varchar(32)   null comment '申请人手机号',
    applicant_id     bigint        null comment '申请人ID',
    return_reason    varchar(1024) not null comment '退货事由',
    return_status    int           not null comment '退货状态:0-未发起，1-待审批，2-已驳回，3-已完结',
    approved_by      bigint        null comment '审批人',
    approved_time    datetime      null comment '审批时间',
    deleted          tinyint(2)    null comment '删除标志（0代表存在，2代表删除）',
    create_by        bigint        null comment '创建人',
    create_time      datetime      null comment '创建时间',
    update_by        bigint        null comment '更新人',
    update_time      datetime      null comment '更新时间',
    purchase_id      bigint        not null comment '关联采购id',
    purchase_code    varchar(64)   not null comment '采购编号'
)
    comment '退货申请表';

-- add by zhoujianyu, 2025.1.15
/* version: 25.1.15.1 start */
create table ns_stock_in
(
    stock_in_id     bigint auto_increment comment '入库记录ID'
        primary key,
    business_type   int            not null comment '业务类型:1-采购入库',
    purchase_id     bigint         not null comment '采购申请',
    purchase_code   varchar(64)    not null comment '关联采购单号',
    warehouse_id    bigint         not null comment '入库仓库',
    warehouse_code  varchar(64)    not null comment '入库仓库编码',
    stock_in_date   datetime       not null comment '入库日期',
    operator        varchar(128)   not null comment '经办人',
    operator_id     bigint         not null comment '经办人ID',
    supplier        varchar(128)   not null comment '供应商',
    supplier_id     bigint         not null comment '供应商ID',
    input_type      int            not null comment '投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他',
    business_id     bigint         not null comment '投入品ID',
    input_category  varchar(64)    not null comment '投入品类别',
    input_name      varchar(128)   not null comment '投入品名称',
    effective_date  datetime       not null comment '有效日期',
    production_date datetime       not null comment '生产日期',
    unit_price      decimal(10, 2) not null comment '采购单价',
    quantity        decimal(10, 2) not null comment '采购数量',
    stock_quantity  decimal(10, 2) not null comment '入库数量',
    deleted         tinyint(2)     null comment '删除标志（0代表存在2代表删除）',
    create_by       bigint         null comment '创建人',
    create_time     datetime       null comment '创建时间',
    update_by       bigint         null comment '更新人',
    update_time     datetime       null comment '更新时间'
)
    comment '入库记录表';
/* version: 25.1.15.2 start */
create table ns_stock_out
(
    stock_out_id   bigint auto_increment comment '出库记录ID'
        primary key,
    business_type  int            not null comment '业务类型:1-领用出库',
    use_id         bigint         not null comment '采购申请',
    use_code       varchar(64)    not null comment '关联采购单号',
    warehouse_id   bigint         not null comment '入库仓库',
    warehouse_code varchar(64)    not null comment '入库仓库编码',
    stock_out_date datetime       not null comment '入库日期',
    operator       varchar(128)   not null comment '经办人',
    operator_id    bigint         not null comment '经办人ID',
    input_type     int            not null comment '投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他',
    business_id    bigint         not null comment '投入品ID',
    input_category varchar(64)    not null comment '投入品类别',
    input_name     varchar(128)   not null comment '投入品名称',
    input_unit     varchar(32)    not null comment '投入品单位',
    stock_quantity decimal(10, 2) not null comment '出库数量',
    deleted        tinyint(2)     null comment '删除标志（0代表存在2代表删除）',
    create_by      bigint         null comment '创建人',
    create_time    datetime       null comment '创建时间',
    update_by      bigint         null comment '更新人',
    update_time    datetime       null comment '更新时间'
)
    comment '出库记录表';
/* version: 25.1.15.3 start */
create table ns_use_application
(
    use_id             bigint auto_increment comment '采购id'
        primary key,
    use_code           varchar(64)   null comment '采购单号',
    applicant          varchar(128)  not null comment '申请人',
    application_date   datetime      not null comment '申请日期',
    applicant_mobile   varchar(32)   null comment '申请人手机号',
    applicant_id       bigint        null comment '申请人ID',
    application_reason varchar(1024) not null comment '申请事由',
    application_status int           not null comment '申请状态:0-未发起，1-待审批，2-已驳回，3-已完结',
    approved_by        bigint        null comment '审批人',
    approved_time      datetime      null comment '审批时间',
    deleted            tinyint(2)    null comment '删除标志（0代表存在，2代表删除）',
    create_by          bigint        null comment '创建人',
    create_time        datetime      null comment '创建时间',
    update_by          bigint        null comment '更新人',
    update_time        datetime      null comment '更新时间'
)
    comment '领用申请表';
/* version: 25.1.15.4 start */
create table ns_use_application_item
(
    item_id        bigint auto_increment comment '子项ID'
        primary key,
    use_id         bigint         not null comment '采购单号',
    input_type     int            not null comment '投入品大类',
    input_category varchar(32)    not null comment '投入品子类',
    business_id    bigint         not null comment '商品ID',
    input_name     varchar(256)   not null comment '投入品名称',
    quantity       decimal(10, 2) not null comment '领用数量',
    unit           varchar(32)    null comment '单位',
    deleted        tinyint(2)     null comment '删除标志（0代表存在，2代表删除）',
    create_by      bigint         null comment '创建人',
    create_time    datetime       null comment '创建时间',
    update_by      bigint         null comment '更新人',
    update_time    datetime       null comment '更新时间'
)
    comment '领用申请子项表';
/* version: 25.1.15.5 start */
create table ns_input_stock_taking
(
    taking_id             bigint auto_increment comment '投入品库存盘点ID'
        primary key,
    business_id           bigint         not null comment '业务ID',
    input_type            int            not null comment '投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他',
    input_category        varchar(64)    not null comment '投入品分类',
    input_sub_category    varchar(64)    null comment '投入品类别',
    input_name            varchar(64)    not null comment '投入品名称',
    stock_quantity        decimal(10, 2) not null comment '库存量',
    taking_stock_quantity decimal(10, 2) not null comment '盘点数量',
    house_id              bigint         not null comment '仓库ID',
    house_name            varchar(64)    not null comment '仓库名称',
    taking_result         int            not null comment '盘点结果:0-正常,1-盘亏，2-盘盈',
    stock_warning_result  varchar(32)    null comment '库存预警状态',
    taking_date           datetime       not null comment '盘点日期',
    taking_operator       varchar(128)   not null comment '盘点人',
    taking_operator_id    bigint         not null comment '盘点人ID',
    deleted               int default 0  not null comment '删除标记',
    create_by             bigint         null comment '创建人',
    create_time           datetime       null comment '创建时间',
    update_by             bigint         null comment '更新人',
    update_time           datetime       null comment '更新时间'
)
    comment '投入品库存盘点表';
/* version: ********* start */
alter table ns_warehouse add column dept_id bigint null comment '农场ID';
/* version: ********* start */
create table ns_customer
(
    customer_id    bigint auto_increment comment '客户ID'
        primary key,
    customer_name  varchar(256) not null comment '客户名称',
    tax_number     varchar(64)  not null comment '税号',
    address        varchar(255) not null comment '地址',
    bank_name      varchar(128) not null comment '开户银行',
    bank_account   varchar(64)  not null comment '银行账号',
    contact_person varchar(128) not null comment '联系人',
    contact_phone  varchar(32)  not null comment '联系人电话',
    deleted        tinyint(2)   null comment '删除标志（0代表存在，2代表删除）',
    create_by      bigint       null comment '创建人',
    create_time    datetime     null comment '创建时间',
    update_by      bigint       null comment '更新人',
    update_time    datetime     null comment '更新时间',
    note           varchar(512) null comment '备注'
)
    comment '客户表';
/* version: ********* start */
create table ns_product_stock_taking
(
    taking_id             bigint auto_increment comment '投入品库存盘点ID'
        primary key,
    product_stock_id      bigint         not null comment '农产品ID',
    crop_type             varchar(64)    not null comment '农产品类别',
    crop_category         varchar(64)    null comment '科别',
    breeds                varchar(64)    null comment '品种',
    crop_name             varchar(64)    not null comment '农产品名称',
    stock_quantity        decimal(10, 2) not null comment '库存量',
    taking_stock_quantity decimal(10, 2) not null comment '盘点数量',
    house_id              bigint         not null comment '仓库ID',
    house_name            varchar(64)    not null comment '仓库名称',
    taking_result         int            not null comment '盘点结果:0-正常,1-盘亏，2-盘盈',
    warning_status        varchar(32)    null comment '库存预警状态',
    taking_date           datetime       not null comment '盘点日期',
    taking_operator       varchar(128)   not null comment '盘点人',
    taking_operator_id    bigint         not null comment '盘点人ID',
    deleted               int default 0  not null comment '删除标记',
    create_by             bigint         null comment '创建人',
    create_time           datetime       null comment '创建时间',
    update_by             bigint         null comment '更新人',
    update_time           datetime       null comment '更新时间'
)
    comment '农产品库存盘点表';
/* version: 25.1.15.9 start */
create table ns_sales_order
(
    order_id           bigint auto_increment comment '订单id'
        primary key,
    sales_order_code   varchar(64)   null comment '销售单号',
    applicant          varchar(128)  not null comment '申请人',
    application_date   datetime      not null comment '申请日期',
    applicant_mobile   varchar(32)   null comment '申请人手机号',
    applicant_id       bigint        null comment '申请人ID',
    customer_id        bigint        null comment '客户ID',
    customer_name      varchar(128)  null comment '客户名称',
    application_reason varchar(1024) not null comment '申请事由',
    application_status int           not null comment '申请状态:0-未发起，1-待审批，2-已驳回，3-已完结',
    approved_by        bigint        null comment '审批人',
    approved_time      datetime      null comment '审批时间',
    deleted            tinyint(2)    null comment '删除标志（0代表存在，2代表删除）',
    create_by          bigint        null comment '创建人',
    create_time        datetime      null comment '创建时间',
    update_by          bigint        null comment '更新人',
    update_time        datetime      null comment '更新时间'
)
    comment '销售订单表';
/* version: 25.1.15.10 start */
create table ns_sales_order_item
(
    item_id          bigint auto_increment comment '子项ID'
        primary key,
    order_id         bigint         not null comment '销售订单ID',
    product_stock_id bigint         not null comment '农产品ID',
    breeds           varchar(128)   not null comment '品种',
    cropName         varchar(128)   not null comment '农产品名称',
    unit_price       decimal(10, 2) not null comment '销售单价',
    unit             varchar(32)    not null comment '单位',
    quantity         decimal(10, 2) not null comment '数量',
    pay_amount       decimal(20, 2) null comment '金额',
    subtotal         decimal(10, 2) not null comment '小计（unit_price*quantity）',
    deleted          tinyint(2)     null comment '删除标志（0代表存在，2代表删除）',
    create_by        bigint         null comment '创建人',
    create_time      datetime       null comment '创建时间',
    update_by        bigint         null comment '更新人',
    update_time      datetime       null comment '更新时间'
)
    comment '销售订单子项表';
/* version: 25.1.15.11 start */
create table ns_sales_order_return
(
    return_id        bigint auto_increment comment '退货id'
        primary key,
    applicant        varchar(128)  not null comment '申请人',
    return_date      datetime      not null comment '退货日期',
    applicant_mobile varchar(32)   null comment '申请人手机号',
    applicant_id     bigint        null comment '申请人ID',
    return_reason    varchar(1024) not null comment '退货事由',
    return_status    int           not null comment '退货状态:0-未发起，1-待审批，2-已驳回，3-已完结',
    approved_by      bigint        null comment '审批人',
    approved_time    datetime      null comment '审批时间',
    deleted          tinyint(2)    null comment '删除标志（0代表存在，2代表删除）',
    create_by        bigint        null comment '创建人',
    create_time      datetime      null comment '创建时间',
    update_by        bigint        null comment '更新人',
    update_time      datetime      null comment '更新时间',
    order_id         bigint        not null comment '销售订单id',
    sales_order_code varchar(64)   not null comment '销售单号'
)
    comment '销售退库表';
/* version: 25.1.15.12 start */
create table ns_product_stock_out
(
    stock_out_id     bigint auto_increment comment '农产品出库记录ID'
        primary key,
    business_type    int            not null comment '业务类型:1-销售出库',
    order_id         bigint         not null comment '采购申请',
    sales_order_code varchar(64)    not null comment '关联销售单号',
    warehouse_id     bigint         not null comment '出库仓库',
    warehouse_code   varchar(64)    not null comment '出库仓库编码',
    stock_out_date   datetime       not null comment '出库日期',
    operator         varchar(128)   not null comment '经办人',
    operator_id      bigint         not null comment '经办人ID',
    product_stock_id bigint         not null comment '农产品ID',
    breeds           varchar(64)    not null comment '品种',
    crop_name        varchar(128)   not null comment '农产品名称',
    input_unit       varchar(32)    not null comment '单位',
    stock_quantity   decimal(10, 2) not null comment '出库数量',
    deleted          tinyint(2)     null comment '删除标志（0代表存在2代表删除）',
    create_by        bigint         null comment '创建人',
    create_time      datetime       null comment '创建时间',
    update_by        bigint         null comment '更新人',
    update_time      datetime       null comment '更新时间'
)
    comment '农产品出库记录表';
/* version: 25.1.15.13 start */
create table ns_factory_inspection
(
    inspection_id            bigint auto_increment comment '检验记录ID'
        primary key,
    operator                 varchar(128) not null comment '检验人',
    operator_id              bigint       not null comment '检验人id',
    production_plan_id       bigint       not null comment '关联生产计划',
    production_plan_code     varchar(64)  null comment '生产计划编号',
    inspection_date          datetime     not null comment '检验日期',
    sample_quantity          int          not null comment '抽样数量',
    product_stock_id         bigint       not null comment '农产品id',
    breeds                   varchar(128) not null comment '品种',
    crop_name                varchar(128) not null comment '农产品名称',
    specification            varchar(128) not null comment '规格',
    record_number            varchar(64)  not null comment '记录编号',
    product_batch_number     varchar(64)  null comment '产品收货批号',
    vehicle_transport_number varchar(64)  null comment '车辆运输编号',
    inspection_content       text         not null comment '检验内容',
    result                   int          not null comment '结果：1-及格，2-不及格',
    deleted                  tinyint(2)   null comment '删除标志（0代表存在2代表删除）',
    create_by                bigint       null comment '创建人',
    create_time              datetime     null comment '创建时间',
    update_by                bigint       null comment '更新人',
    update_time              datetime     null comment '更新时间'
)
    comment '出厂检验表';

-- add by zhoujianyu, 2025.1.23
/* version: 25.1.23.1 start */
create table ns_device
(
    device_id   bigint       not null comment '设备id'
        primary key,
    dept_id     bigint       null comment '农场ID',
    file        varchar(128) not null comment '附件',
    device_code varchar(64)  not null comment '设备编号',
    deleted     tinyint(2)   null comment '删除标志',
    create_by   bigint       null comment '创建人',
    create_time datetime     null comment '创建时间',
    update_by   bigint       null comment '更新人',
    update_time datetime     null comment '更新时间'
)
    comment '农场设备表';

/* version: 25.1.23.2 start */
alter table ns_sales_order_item
    add column scrap_quantity decimal(10, 2) null comment '报废数量';


/* version: 25.1.23.3 start */
create table ns_pesticide_residue_test
(
    test_id          bigint auto_increment comment '检测id'
        primary key,
    inspector        varchar(128) not null comment '检查人',
    inspector_id     bigint       not null comment '检查人id',
    test_time        datetime     not null comment '检测时间',
    sales_order_id   bigint       not null comment '销售订单id，关联销售订单表的订单id',
    sales_order_code varchar(128) not null comment '销售订单号，关联销售订单表的订单号',
    deleted          tinyint(2)   null comment '删除标志（0代表存在，2代表删除）',
    create_by        bigint       null comment '创建人',
    create_time      datetime     null comment '创建时间',
    update_by        bigint       null comment '更新人',
    update_time      datetime     null comment '更新时间'
)
    comment '农残检测表';

/* version: 25.1.23.4 start */
create table ns_pesticide_residue_test_item
(
    item_id          bigint auto_increment comment '农残检测子项id'
        primary key,
    test_id          bigint         not null comment '检测id，关联农残检测表的检测id',
    product_stock_id bigint         not null comment '农产品ID',
    breeds           varchar(128)   not null comment '品种',
    cropName         varchar(128)   not null comment '农产品名称',
    residue          decimal(10, 2) not null comment '农残抑制率',
    concentration    decimal(10, 2) not null comment '农残浓度',
    result           int            not null comment '检测结果,1-及格，2-不及格',
    deleted          tinyint(2)     null comment '删除标志（0代表存在，2代表删除）',
    create_by        bigint         null comment '创建人',
    create_time      datetime       null comment '创建时间',
    update_by        bigint         null comment '更新人',
    update_time      datetime       null comment '更新时间'
)
    comment '农残检测子项表';

-- add by zhoujianyu, 2025.2.5
/* version: ********* start */
alter table ns_customer
    add column province varchar(64) null comment '省' after address;

/* version: ********* start */
alter table ns_customer
    add column city varchar(64) null comment '市' after province;

/* version: ********* start */
alter table ns_production_plan
    add column  is_binding int null comment '是否绑定';

/* version: ********* start */
alter table ns_production_plan
    add column  current_progress varchar(64) null comment '当前进度';

-- add by zhoujianyu, 2025.2.26
/* version: ********* start */
alter table ns_agricultural_materials
    add column stock_warning_line decimal(20, 2) null comment '库存预警线';

/* version: ********* start */
alter table ns_fertilizer
    add column stock_warning_line decimal(20, 2) null comment '库存预警线';

-- add by zhoujianyu, 2025.2.27
/* version: ********* start */
alter table ns_production_plan
    add column order_id bigint(20) null comment '订单id',
    add column sales_order_code varchar(64) default '' comment '订单编号';

/* version: ********* start */
alter table ns_sys_dept
    add column access_dept_ids varchar(512) null comment '可访问的部门ids';

-- add by zhoujianyu, 2025.2.28
/* version: 25.2.28.1 start */
alter table ns_warehouse
    change dept_id region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.2 start */
update ns_warehouse
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.3 start */
alter table ns_use_application
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.4 start */
update ns_use_application
set region_dept_id = 100
where region_dept_id is null;


/* version: 25.2.28.5 start */
alter table ns_supplier
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.6 start */
update ns_supplier
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.7 start */
alter table ns_stock_out
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.8 start */
update ns_stock_out
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.9 start */
alter table ns_stock_in
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.10 start */
update ns_stock_in
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.11 start */
alter table ns_semi_product_inbound
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.12 start */
update ns_semi_product_inbound
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.13 start */
alter table ns_sales_order_return
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.14 start */
update ns_sales_order_return
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.15 start */
alter table ns_sales_order
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.16 start */
update ns_sales_order
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.17 start */
alter table ns_return_application
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.18 start */
update ns_return_application
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.19 start */
alter table ns_purchase_record
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.20 start */
update ns_purchase_record
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.21 start */
alter table ns_purchase_application
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.22 start */
update ns_purchase_application
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.23 start */
alter table ns_product_stock_taking
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.24 start */
update ns_product_stock_taking
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.25 start */
alter table ns_product_stock_out
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.26 start */
update ns_product_stock_out
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.27 start */
alter table ns_product_stock
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.28 start */
update ns_product_stock
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.29 start */
alter table ns_product_scrap
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.30 start */
update ns_product_scrap
set region_dept_id = 100
where region_dept_id is null;


/* version: 25.2.28.31 start */
alter table ns_product
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.32 start */
update ns_product
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.33 start */
alter table ns_planting_standard
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.34 start */
update ns_planting_standard
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.35 start */
alter table ns_pesticide_residue_test
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.36 start */
update ns_pesticide_residue_test
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.37 start */
alter table ns_input_stock_taking
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.38 start */
update ns_input_stock_taking
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.39 start */
alter table ns_input_stock
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.40 start */
update ns_input_stock
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.41 start */
alter table ns_input_other
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.42 start */
update ns_input_other
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.43 start */
alter table ns_fertilizer
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.44 start */
update ns_fertilizer
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.45 start */
alter table ns_farm_machinery
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.46 start */
update ns_farm_machinery
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.47 start */
alter table ns_farm_film
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.48 start */
update ns_farm_film
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.49 start */
alter table ns_factory_inspection
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.50 start */
update ns_factory_inspection
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.51 start */
alter table ns_customer
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.52 start */
update ns_customer
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.53 start */
alter table ns_crop_breeds
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.54 start */
update ns_crop_breeds
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.55 start */
alter table ns_agricultural_materials
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.56 start */
update ns_agricultural_materials
set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.57 start */
alter table ns_production_plan
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.58 start */
update ns_production_plan set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.59 start */
alter table ns_vacation_transfer
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.60 start */
update ns_vacation_transfer set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.61 start */
alter table ns_my_knowledge_base
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.62 start */
update ns_my_knowledge_base set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.63 start */
alter table ns_audit_maintain
    add column region_dept_id bigint(20) null comment '农场id';

/* version: 25.2.28.64 start */
update ns_audit_maintain set region_dept_id = 100
where region_dept_id is null;

/* version: 25.2.28.65 start */
alter table ns_audit_execute
    add column region_dept_id bigint(20) null comment '农场id';

/* version: ********** start */
update ns_audit_execute set region_dept_id = 100
where region_dept_id is null;

/* version: ********** start */
alter table ns_prod_audit
    add column region_dept_id bigint(20) null comment '农场id';

/* version: ********** start */
update ns_prod_audit set region_dept_id = 100
where region_dept_id is null;

/* version: ********** start */
alter table ns_sys_equipment
    add column region_dept_id bigint(20) null comment '农场id';

/* version: ********** start */
update ns_sys_equipment set region_dept_id = 100
where region_dept_id is null;

/* version: ********** start */
alter table ns_sys_dept
add column latitude varchar(128) null comment '纬度',
add column longitude varchar(128) null comment '经度';

-- add by zhoujianyu, 2025.2.29
/* version: ********* start */
alter table ns_sys_dept
    add column  license varchar(255) null comment '营业执照';

/* version: ********* start */
alter table ns_farming_process
    add column  region_dept_id bigint(20) null comment '农场id';

/* version: ********* start */
update ns_farming_process
set region_dept_id = 100
where region_dept_id is null;

-- add by zhoujianyu, 2025.2.30
/* version: ********* start */
alter table ns_input_stock_taking
add column  warning_status varchar(32) null comment '预警状态';


-- add by zhoujianyu, 2025.2.31
/* version: ********* start */
alter  table ns_t_image_tmp
add column file_name varchar(255) null comment '文件名';

/* version: ********* start */
alter  table ns_t_image
    add column file_name varchar(255) null comment '文件名';

-- add by zhoujianyu, 2025.2.32
/* version: 25.2.32.1 start */
alter table ns_factory_inspection
    add column remark varchar(512) null comment '备注';

/* version: 25.2.32.2 start */
alter table ns_pesticide_residue_test
    add column remark varchar(512) null comment '备注';

-- add by zhoujianyu, 2025.3.4
/* version: 25.3.4.1 start */
alter table ns_stock_in
    modify effective_date datetime null comment '有效日期';
/* version: 25.3.4.2 start */
alter table ns_stock_in
    modify production_date datetime null comment '生产日期';

-- add by zhoujianyu, 2025.3.6
/* version: 25.3.6.1 start */
alter table ns_pesticide_residue_test
    add column  production_plan_id bigint(20) null comment '生产计划id',
    add column  production_plan_code varchar(64) null comment '生产计划编号';
