<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 自定义配置 Mapper 表：ns_region (区块)
 另基础 Mapper 请参考：base/business/NsRegionBaseDao.xml
-->
<mapper namespace="com.jorchi.business.dao.NsRegionDao">

    <!-- 按Form对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where_By_Form">
        <if test="regionId != null">
            and region_id = #{regionId}
        </if>
        <if test="regionName != null and regionName != ''">
            and region_name like concat('%', #{regionName}, '%')
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
        <if test="regionDeptName != null and regionDeptName != ''">
            and region_dept_name like concat('%', #{regionDeptName}, '%')
        </if>
        <if test="regionArea != null and regionArea != ''">
            and region_area like concat('%', #{regionArea}, '%')
        </if>
        <if test="regionLeaderId != null">
            and region_leader_id = #{regionLeaderId}
        </if>
        <if test="regionLeaderName != null and regionLeaderName != ''">
            and region_leader_name like concat('%', #{regionLeaderName}, '%')
        </if>
        <if test="regionPoints != null and regionPoints != ''">
            and region_points like concat('%', #{regionPoints}, '%')
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="deleted == null">
            and deleted = 0
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
    </sql>

    <sql id="Base_Column_List_By_Role">
        r.region_id,
        r.region_name,
        r.region_dept_id,
        r.region_dept_name,
        r.region_area,
        r.region_leader_id,
        r.region_leader_name,
        r.region_points,
        r.deleted,
        r.create_by,
        r.create_time,
        r.update_by,
        r.update_time
    </sql>
    <sql id="Base_Select_By_Entity_Where_By_Form_By_Role">
        <if test="regionId != null">
            and region_id = #{regionId}
        </if>
        <if test="regionName != null and regionName != ''">
            and region_name like concat('%', #{regionName}, '%')
        </if>
        <if test="regionDeptName != null and regionDeptName != ''">
            and region_dept_name like concat('%', #{regionDeptName}, '%')
        </if>
        <if test="regionArea != null and regionArea != ''">
            and region_area like concat('%', #{regionArea}, '%')
        </if>
        <if test="regionLeaderId != null">
            and region_leader_id = #{regionLeaderId}
        </if>
        <if test="regionLeaderName != null and regionLeaderName != ''">
            and region_leader_name like concat('%', #{regionLeaderName}, '%')
        </if>
        <if test="regionPoints != null and regionPoints != ''">
            and region_points like concat('%', #{regionPoints}, '%')
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="deleted == null">
            and deleted = 0
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>


    <select id="selectPageByForm" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_By_Role"/>
        from ns_region r
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form_By_Role"/>
        </where>
    </select>
    <!-- 没有下级权限的用户按Form对象查询一页记录（多条记录） -->
    <select id="selectPageByFormNoRole" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_By_Role"/>
        from ns_region r
        left join ns_sys_dept d on r.region_dept_id = d.dept_id
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form"/>
        </where>
    </select>

    <!-- 请在此处添加其它自定义配置-->
    <select id="getTotal" resultType="java.lang.Integer">
        select count(1) from ns_house where house_region_id = #{primaryKey} and deleted = 0
    </select>

    <!--    查询各个区块面积总和-->
    <select id="selectArea" resultType="java.util.Map">
        SELECT
            sum( house_area ) AS sumArea,
            house_region_id AS id
        FROM
            ns_house
        WHERE
            deleted = 0
        GROUP BY
            house_region_id
    </select>
    <select id="selectByDeptIds" resultType="com.jorchi.business.po.NsRegion">
        SELECT  <include refid="Base_Column_List"/>
        from ns_region
        where region_dept_id in
        <foreach item="deptIds" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>
</mapper>
