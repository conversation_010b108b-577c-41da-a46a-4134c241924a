<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 自定义配置 Mapper 表：ns_product_scrap (报废表)
 另基础 Mapper 请参考：base/business/NsProductScrapBaseDao.xml
-->
<mapper namespace="com.jorchi.business.dao.NsProductScrapDao">

    <!-- 按Form对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where_By_Form">
        and deleted = 0

        <if test="scrapId != null">
            and scrap_id = #{scrapId}
        </if>
        <if test="applicant != null">
            and applicant = #{applicant}
        </if>
        <if test="applicantName != null and applicantName != ''">
            and applicant_name like concat('%', #{applicantName}, '%')
        </if>
        <if test="businessType != null">
            and business_type = #{businessType}
        </if>
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and warehouse_code like concat('%', #{warehouseCode}, '%')
        </if>
        <if test="orderId != null and orderId != ''">
            and order_id like concat('%', #{orderId}, '%')
        </if>
        <if test="productionPlanId != null">
            and production_plan_id = #{productionPlanId}
        </if>
        <if test="semiProductId != null">
            and semi_product_id = #{semiProductId}
        </if>
        <if test="productId != null">
            and product_id = #{productId}
        </if>
        <if test="cropType != null and cropType != ''">
            and crop_type like concat('%', #{cropType}, '%')
        </if>
        <if test="cropName != null and cropName != ''">
            and crop_name like concat('%', #{cropName}, '%')
        </if>
        <if test="scrapQuantity != null">
            and scrap_quantity = #{scrapQuantity}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
                <if test="regionDeptId !=null">
            and region_dept_id = #{regionDeptId}
        </if>

    </sql>

    <!-- 按Form对象查询一页记录（多条记录） -->
    <select id="selectPageByForm" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
          from ns_product_scrap
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form" />
        </where>
    </select>

    <!-- 请在此处添加其它自定义配置-->
</mapper>
