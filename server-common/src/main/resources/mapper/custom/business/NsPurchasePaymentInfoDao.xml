<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 自定义配置 Mapper 表：ns_purchase_payment_info (采购支付信息表)
 另基础 Mapper 请参考：base/business/NsPurchasePaymentInfoBaseDao.xml
-->
<mapper namespace="com.jorchi.business.dao.NsPurchasePaymentInfoDao">

    <!-- 按Form对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where_By_Form">
        <if test="paymentInfoId != null">
            and payment_info_id = #{paymentInfoId}
        </if>
        <if test="purchaseRecordId != null">
            and purchase_record_id = #{purchaseRecordId}
        </if>
        <if test="supplierId != null">
            and supplier_id = #{supplierId}
        </if>
        <if test="supplierName != null and supplierName != ''">
            and supplier_name like concat('%', #{supplierName}, '%')
        </if>
        <if test="supplierBank != null and supplierBank != ''">
            and supplier_bank like concat('%', #{supplierBank}, '%')
        </if>
        <if test="supplierAccount != null and supplierAccount != ''">
            and supplier_account like concat('%', #{supplierAccount}, '%')
        </if>
        <if test="paymentAmount != null">
            and payment_amount = #{paymentAmount}
        </if>
        <if test="attachment != null and attachment != ''">
            and attachment like concat('%', #{attachment}, '%')
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
        and deleted = 0

    </sql>

    <!-- 按Form对象查询一页记录（多条记录） -->
    <select id="selectPageByForm" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
          from ns_purchase_payment_info
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form" />
        </where>
    </select>

    <!-- 请在此处添加其它自定义配置-->
</mapper>
