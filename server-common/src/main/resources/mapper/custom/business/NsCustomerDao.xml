<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 自定义配置 Mapper 表：ns_customer (客户表)
 另基础 Mapper 请参考：base/business/NsCustomerBaseDao.xml
-->
<mapper namespace="com.jorchi.business.dao.NsCustomerDao">

    <!-- 按Form对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where_By_Form">
        <if test="customerId != null">
            and customer_id = #{customerId}
        </if>
        <if test="customerName != null and customerName != ''">
            and customer_name like concat('%', #{customerName}, '%')
        </if>
        <if test="taxNumber != null and taxNumber != ''">
            and tax_number like concat('%', #{taxNumber}, '%')
        </if>
        <if test="address != null and address != ''">
            and address like concat('%', #{address}, '%')
        </if>
        <if test="bankName != null and bankName != ''">
            and bank_name like concat('%', #{bankName}, '%')
        </if>
        <if test="bankAccount != null and bankAccount != ''">
            and bank_account like concat('%', #{bankAccount}, '%')
        </if>
        <if test="contactPerson != null and contactPerson != ''">
            and contact_person like concat('%', #{contactPerson}, '%')
        </if>
        <if test="contactPhone != null and contactPhone != ''">
            and contact_phone like concat('%', #{contactPhone}, '%')
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
        <if test="note != null and note != ''">
            and note like concat('%', #{note}, '%')
        </if>
        <if test="regionDeptId !=null">
            and region_dept_id = #{regionDeptId}
        </if>

        and deleted = 0
    </sql>

    <!-- 按Form对象查询一页记录（多条记录） -->
    <select id="selectPageByForm" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
          from ns_customer
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form" />
        </where>
    </select>

    <!-- 请在此处添加其它自定义配置-->
    <select id="selectCustomerNum" resultType="java.lang.Integer">
        select count(1) from ns_customer
        where deleted = 0
        and region_dept_id = #{regionDeptId}
    </select>
    <select id="selectCustomerCityNum" resultType="java.lang.Integer">
        select count(distinct city) from ns_customer
        where deleted = 0
        and city is not null
        and region_dept_id = #{regionDeptId}
    </select>
</mapper>
