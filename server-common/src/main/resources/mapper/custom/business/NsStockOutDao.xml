<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 自定义配置 Mapper 表：ns_stock_out (出库记录表)
 另基础 Mapper 请参考：base/business/NsStockOutBaseDao.xml
-->
<mapper namespace="com.jorchi.business.dao.NsStockOutDao">

    <!-- 按Form对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where_By_Form">
        <if test="stockOutId != null">
            and stock_out_id = #{stockOutId}
        </if>
        <if test="businessType != null">
            and business_type = #{businessType}
        </if>
        <if test="useId != null">
            and use_id = #{useId}
        </if>
        <if test="useCode != null and useCode != ''">
            and use_code like concat('%', #{useCode}, '%')
        </if>
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and warehouse_code like concat('%', #{warehouseCode}, '%')
        </if>
        <if test="stockOutDate != null">
            and stock_out_date = #{stockOutDate}
        </if>
        <if test="stockOutDateFrom != null">
            and stock_out_date >= #{stockOutDateFrom}
        </if>
        <if test="stockOutDateTo != null">
            <![CDATA[
            and stock_out_date <= #{stockOutDateTo}
            ]]>
        </if>
        <if test="operator != null and operator != ''">
            and operator like concat('%', #{operator}, '%')
        </if>
        <if test="operatorId != null">
            and operator_id = #{operatorId}
        </if>
        <if test="inputType != null">
            and input_type = #{inputType}
        </if>
        <if test="businessId != null">
            and business_id = #{businessId}
        </if>
        <if test="inputCategory != null and inputCategory != ''">
            and input_category like concat('%', #{inputCategory}, '%')
        </if>
        <if test="inputName != null and inputName != ''">
            and input_name like concat('%', #{inputName}, '%')
        </if>
        <if test="inputUnit != null and inputUnit != ''">
            and input_unit like concat('%', #{inputUnit}, '%')
        </if>
        <if test="stockQuantity != null">
            and stock_quantity = #{stockQuantity}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
                <if test="regionDeptId !=null">
            and region_dept_id = #{regionDeptId}
        </if>

        and deleted = 0

    </sql>

    <!-- 按Form对象查询一页记录（多条记录） -->
    <select id="selectPageByForm" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
          from ns_stock_out
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form" />
        </where>
    </select>

    <!-- 请在此处添加其它自定义配置-->
</mapper>
