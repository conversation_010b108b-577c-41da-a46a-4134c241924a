<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 自定义配置 Mapper 表：ns_house (大棚)
 另基础 Mapper 请参考：base/business/NsHouseBaseDao.xml
-->
<mapper namespace="com.jorchi.business.dao.NsHouseDao">

    <!-- 按Form对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where_By_Form">
        <if test="houseId != null">
            and house_id = #{houseId}
        </if>
        <if test="houseName != null and houseName != ''">
            and house_name like concat('%', #{houseName}, '%')
        </if>
        <if test="houseRegionId != null">
            and house_region_id = #{houseRegionId}
        </if>
        <if test="houseRegionName != null and houseRegionName != ''">
            and house_region_name like concat('%', #{houseRegionName}, '%')
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
        <if test="regionDeptName != null and regionDeptName != ''">
            and region_dept_name like concat('%', #{regionDeptName}, '%')
        </if>
        <if test="houseArea != null and houseArea != ''">
            and house_area like concat('%', #{houseArea}, '%')
        </if>
        <if test="technicianId != null">
            and technician_id = #{technicianId}
        </if>
        <if test="technicianName != null and technicianName != ''">
            and technician_name like concat('%', #{technicianName}, '%')
        </if>
        <if test="technicianAgentId != null">
            and technician_agent_id = #{technicianAgentId}
        </if>
        <if test="technicianAgentName != null and technicianAgentName != ''">
            and technician_agent_name like concat('%', #{technicianAgentName}, '%')
        </if>
        <if test="housePoints != null and housePoints != ''">
            and house_points like concat('%', #{housePoints}, '%')
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
            and deleted = 0 order by house_name
    </sql>
    <sql id="Base_Select_By_Entity_Where_By_Form_By_Role">
        <if test="houseId != null">
            and house_id = #{houseId}
        </if>
        <if test="houseName != null and houseName != ''">
            and house_name like concat('%', #{houseName}, '%')
        </if>
        <if test="houseRegionId != null">
            and house_region_id = #{houseRegionId}
        </if>
        <if test="houseRegionName != null and houseRegionName != ''">
            and house_region_name like concat('%', #{houseRegionName}, '%')
        </if>
        <if test="regionDeptName != null and regionDeptName != ''">
            and region_dept_name like concat('%', #{regionDeptName}, '%')
        </if>
        <if test="houseArea != null and houseArea != ''">
            and house_area like concat('%', #{houseArea}, '%')
        </if>
        <if test="technicianId != null">
            and technician_id = #{technicianId}
        </if>
        <if test="technicianName != null and technicianName != ''">
            and technician_name like concat('%', #{technicianName}, '%')
        </if>
        <if test="technicianAgentId != null">
            and technician_agent_id = #{technicianAgentId}
        </if>
        <if test="technicianAgentName != null and technicianAgentName != ''">
            and technician_agent_name like concat('%', #{technicianAgentName}, '%')
        </if>
        <if test="housePoints != null and housePoints != ''">
            and house_points like concat('%', #{housePoints}, '%')
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
        <if test="regionDeptId !=null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>
    <sql id="Base_Column_List_By_Role">
        h.house_id,
        h.house_name,
        h.house_region_id,
        h.house_region_name,
        h.region_dept_id,
        h.region_dept_name,
        h.house_area,
        h.technician_id,
        h.technician_name,
        h.technician_agent_id,
        h.technician_agent_name,
        h.house_points,
        h.is_plant,
        h.process_stage,
        h.deleted,
        h.create_by,
        h.create_time,
        h.update_by,
        h.update_time,
        h.image_path
    </sql>
    <!-- 按Form对象查询一页记录（多条记录） -->
    <select id="selectPageByForm" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_By_Role"/>
          from ns_house h
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form_By_Role"/>
        </where>
    </select>
    <!-- 没有下级权限的用户按Form对象查询一页记录（多条记录） -->
    <select id="selectPageByFormNoRole" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_By_Role"/>
        from ns_house h
        left join ns_sys_dept d on h.region_dept_id = d.dept_id
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form"/>
            and h.deleted = 0
        </where>
    </select>
    <!-- 按houseRegionName来查询各分区总数及在植大棚数 -->
    <select id="getByRegionNameCount" resultType="com.jorchi.business.vo.NsPlantingVo">
        SELECT house_region_name as houseRegionName,is_plant as isPlant FROM ns_house
        <where>
            <if test="regionName != null and regionName != ''">
                AND house_region_name = #{regionName}
                and deleted = 0
            </if>
        </where>
    </select>

    <!-- 请在此处添加其它自定义配置-->

    <select id="selectTotal" resultType="int">
        select count(1) from ns_house where house_region_id =#{id} and deleted = 0
    </select>

    <select id="selectPlanted" resultType="int">
SELECT
	COUNT(a.house_id)
FROM
	ns_production_plan a
	LEFT JOIN ns_house b ON a.house_id = b.house_id
WHERE
	b.deleted = 0
	AND a.harvest_date > NOW()
	AND a.deleted = FALSE
	AND a.plant_id = #{id}
	GROUP BY a.plant_id
    </select>

<!--    检查大棚编号是否重复-->
    <select id="isExist" resultType="java.lang.Integer">
        select count(1) from ns_house where house_name = #{houseName} and deleted = 0
        and region_dept_id = #{regionDeptId}
    </select>

<!--    检查是否存在相同的大棚编号(除了自己编号以外)-->
    <select id="isExistBesidesSelf" resultType="java.lang.Integer">
        select count(1)
          from ns_house
         where house_name = #{houseName} and house_id != #{houseId} and deleted = 0
    </select>

<!--    获取相对应的流程阶段-->
    <select id="selectProcess" resultType="java.lang.Long">
        SELECT
            nfp.process_stage AS processStage
        FROM
            ns_farming_process nfp
                LEFT JOIN ns_task nt ON nt.farming_process_id = nfp.farming_process_id
                LEFT JOIN ns_production_plan npp ON npp.id = nt.ns_production_plan_id
                LEFT JOIN ns_house nh ON nh.house_id = npp.house_id
        WHERE
            nh.house_id = #{houseId}
          AND ( CURDATE() BETWEEN nt.start_time AND nt.end_time )
          AND npp.deleted = 0
        ORDER BY
            ABS(
                    nt.start_time - CURDATE())
            LIMIT 1
    </select>

<!--    查询所有的大棚-->
    <select id="selectAllHouse" resultMap="BaseResultMap" >
        select <include refid="Base_Column_List" />
        from ns_house
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form" />
            and deleted = 0
        </where>
    </select>

<!--    更新流程阶段发生变化的大棚-->
    <update id="updateHouseProgress">
        update ns_house set process_stage = #{processStage} where house_id = #{houseId}
    </update>

<!--    查看大棚是否还有生产计划-->
    <select id="selectPlan" resultType="java.lang.Integer">
        SELECT
            count( nt.task_id )
        FROM
            ns_task nt
                LEFT JOIN ns_production_plan npp ON nt.ns_production_plan_id = npp.id
                left join ns_house nh on nh.house_id = npp.house_id
        where nh.house_id = #{houseId} and (nt.STATUS != 1 or nt.STATUS != 3)and npp.deleted = 0 and nt.deleted = 0
    </select>

<!--    无生产计划时，当前状态若不是空棚，则设置成空棚-->
    <update id="updateNullHouse">
        update ns_house set process_stage = 164 where house_id = #{houseId} and deleted = 0
    </update>



<!--    获取大棚地图图例-->
    <select id="selectMapLegend" resultType="com.jorchi.business.vo.MapLegend">
        SELECT
            dict_code AS dictCode,
            dict_label AS name,
            dict_value AS color
        FROM
            ns_sys_dict_data
        WHERE
            dict_type = 'hourse_color'
          AND STATUS = 0
        ORDER BY
            dict_code ASC
    </select>

<!--    查找当前大棚的状态及颜色-->
    <select id="selectHouseStatus" resultType="java.util.Map">
        select dict_value as color,dict_label AS name
          from ns_sys_dict_data
         where dict_code = #{processStage}
    </select>

<!--    小程序根据状态筛选大棚-->
    <select id="selectStatus" resultType="com.jorchi.business.po.NsHouse">
        select distinct house_name as houseName from ns_house where process_stage = #{dictId} and house_region_id = #{regionId} and deleted =0
    </select>

<!--    根据大棚Id查找大棚名称-->
    <select id="selectHouseById" resultType="java.lang.String">
        select distinct house_name from ns_house where house_id =#{houseId} and deleted = 0 limit 1
    </select>

<!--    查找大棚最新数据采集记录-->
    <select id="selectRecentData" resultType="java.util.Map">
        select q,t,d from ns_house_soil_collect_data where house_id = #{houseId} and deleted = 0 order by d desc limit 1
    </select>

<!--    查找空棚大棚数-->
    <select id="selectEmpty" resultType="java.lang.Integer">
        select count(house_id) from ns_house where house_region_id = #{regionId} and deleted =0 and process_stage = 164
    </select>

<!--    查找大棚数量，大棚面积总和-->
    <select id="selectCount" resultType="java.util.Map">
        select count(house_id) as houseNum,sum(house_area) as houseArea from ns_house where deleted = 0
        and region_dept_id = #{regionDeptId};
    </select>

<!--    统计各状态大棚的数据-->
    <select id="selectHouseStatics" resultType="java.util.Map">
        SELECT
            process_stage as color,
            COUNT( process_stage ) as num,
            sum( house_area ) as area
        FROM
            ns_house
        WHERE
            deleted = 0
        and region_dept_id = #{regionDeptId}
        GROUP BY
            process_stage
    </select>
    <!--    根据名称查询大棚-->
    <select id="selectByHouseName" resultType="com.jorchi.business.po.NsHouse">
        select <include refid="Base_Column_List" />
        from ns_house
        where house_name = #{houseName}
        and deleted = 0
        and region_dept_id = #{regionDeptId}
    </select>

    <!--    计划完结-->
    <update id="completePlan">
        update ns_production_plan set complete_flag = 1 where house_id = #{houseId}
    </update>
</mapper>
