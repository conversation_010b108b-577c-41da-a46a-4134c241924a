<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="com.jorchi.project.image.dao.TImageDao">
	<!-- 请在下方添加自定义配置-->

	<resultMap id="ImageTmpResultMap" type="com.jorchi.project.image.po.TImageTmp">
		<id column="code" property="code" />
		<result column="path" property="path" />
		<result column="create_time" property="createTime" />
		<result column="expired_time" property="expiredTime" />
	</resultMap>

	<select id="selectOverDue" resultMap="ImageTmpResultMap">
		select code, path, create_time, expired_time
		from ns_t_image
		<where>
			<![CDATA[
			expired_time < #{expiredTime}
		]]>
		</where>
	</select>
    <select id="selectByPrimaryKeys" resultType="com.jorchi.project.image.po.TImage">
		select
		<include refid="Base_Column_List" />
		from ns_t_image
		where code in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
</mapper>
