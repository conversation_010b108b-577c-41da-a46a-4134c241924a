<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_farming_process (农事流程)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsFarmingProcessDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsFarmingProcess">
        <id column="farming_process_id" property="farmingProcessId" />
        <result column="process_name" property="processName" />
        <result column="input_type" property="inputType" />
        <result column="input_time" property="inputTime" />
        <result column="remark" property="remark" />
        <result column="process_stage" property="processStage" />
        <result column="create_id" property="createId" />
        <result column="create_time" property="createTime" />
        <result column="update_id" property="updateId" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="region_dept_id" property="regionDeptId" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        farming_process_id,
        process_name,
        input_type,
        input_time,
        remark,
        process_stage,
        create_id,
        create_time,
        update_id,
        update_time,
        deleted,
        region_dept_id
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="farmingProcessId != null">
            and farming_process_id = #{farmingProcessId}
        </if>
        <if test="processName != null and processName != ''">
            and process_name = #{processName}
        </if>
        <if test="inputType != null">
            and input_type = #{inputType}
        </if>
        <if test="inputTime != null and inputTime != ''">
            and input_time = #{inputTime}
        </if>
        <if test="remark != null and remark != ''">
            and remark = #{remark}
        </if>
        <if test="processStage != null">
            and process_stage = #{processStage}
        </if>
        <if test="createId != null">
            and create_id = #{createId}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateId != null">
            and update_id = #{updateId}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_farming_process
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_farming_process
         where farming_process_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_farming_process
         where farming_process_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_farming_process
         where farming_process_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_farming_process
         where farming_process_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.farmingProcessId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_farming_process
         where farming_process_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_farming_process
         where farming_process_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_farming_process
         where farming_process_id = #{farmingProcessId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_farming_process
         where farming_process_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.farmingProcessId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_farming_process
        <trim prefix="(" suffix=")" suffixOverrides="," >
            farming_process_id,
            process_name,
            input_type,
            input_time,
            remark,
            process_stage,
            create_id,
            create_time,
            update_id,
            update_time,
            deleted,
            region_dept_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{farmingProcessId},
            #{processName},
            #{inputType},
            #{inputTime},
            #{remark},
            #{processStage},
            #{createId},
            #{createTime},
            #{updateId},
            #{updateTime},
            #{deleted},
            #{regionDeptId}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_farming_process
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="farmingProcessId != null" >
                farming_process_id,
            </if>
            <if test="processName != null" >
                process_name,
            </if>
            <if test="inputType != null" >
                input_type,
            </if>
            <if test="inputTime != null" >
                input_time,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="processStage != null" >
                process_stage,
            </if>
            <if test="createId != null" >
                create_id,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateId != null" >
                update_id,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="farmingProcessId != null" >
                #{farmingProcessId},
            </if>
            <if test="processName != null" >
                #{processName},
            </if>
            <if test="inputType != null" >
                #{inputType},
            </if>
            <if test="inputTime != null" >
                #{inputTime},
            </if>
            <if test="remark != null" >
                #{remark},
            </if>
            <if test="processStage != null" >
                #{processStage},
            </if>
            <if test="createId != null" >
                #{createId},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateId != null" >
                #{updateId},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_farming_process
        <trim prefix="(" suffix=")" suffixOverrides="," >
            farming_process_id,
            process_name,
            input_type,
            input_time,
            remark,
            process_stage,
            create_id,
            create_time,
            update_id,
            update_time,
            deleted,
            region_dept_id
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.farmingProcessId},
                #{item.processName},
                #{item.inputType},
                #{item.inputTime},
                #{item.remark},
                #{item.processStage},
                #{item.createId},
                #{item.createTime},
                #{item.updateId},
                #{item.updateTime},
                #{item.deleted},
                #{item.regionDeptId}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_farming_process
          <set>
            <if test="processName != null" >
                process_name=#{processName},
            </if>
            <if test="inputType != null" >
                input_type=#{inputType},
            </if>
            <if test="inputTime != null" >
                input_time=#{inputTime},
            </if>
            <if test="remark != null" >
                remark=#{remark},
            </if>
            <if test="processStage != null" >
                process_stage=#{processStage},
            </if>
            <if test="createId != null" >
                create_id=#{createId},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateId != null" >
                update_id=#{updateId},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
          </set>
         where farming_process_id = #{farmingProcessId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_farming_process
          <set>
                process_name=#{processName},
                input_type=#{inputType},
                input_time=#{inputTime},
                remark=#{remark},
                process_stage=#{processStage},
                create_id=#{createId},
                create_time=#{createTime},
                update_id=#{updateId},
                update_time=#{updateTime},
                deleted=#{deleted},
                region_dept_id=#{regionDeptId},
          </set>
         where farming_process_id = #{farmingProcessId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
