<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_product_scrap (报废表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsProductScrapDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsProductScrap">
        <id column="scrap_id" property="scrapId" />
        <result column="applicant" property="applicant" />
        <result column="applicant_name" property="applicantName" />
        <result column="business_type" property="businessType" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="order_id" property="orderId" />
        <result column="production_plan_id" property="productionPlanId" />
        <result column="semi_product_id" property="semiProductId" />
        <result column="product_id" property="productId" />
        <result column="crop_type" property="cropType" />
        <result column="crop_name" property="cropName" />
        <result column="scrap_quantity" property="scrapQuantity" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="region_dept_id" property="regionDeptId" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        scrap_id,
        applicant,
        applicant_name,
        business_type,
        warehouse_id,
        warehouse_code,
        order_id,
        production_plan_id,
        semi_product_id,
        product_id,
        crop_type,
        crop_name,
        scrap_quantity,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        region_dept_id
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="scrapId != null">
            and scrap_id = #{scrapId}
        </if>
        <if test="applicant != null">
            and applicant = #{applicant}
        </if>
        <if test="applicantName != null and applicantName != ''">
            and applicant_name = #{applicantName}
        </if>
        <if test="businessType != null">
            and business_type = #{businessType}
        </if>
        <if test="warehouseId != null">
            and warehouse_id = #{warehouseId}
        </if>
        <if test="warehouseCode != null and warehouseCode != ''">
            and warehouse_code = #{warehouseCode}
        </if>
        <if test="orderId != null and orderId != ''">
            and order_id = #{orderId}
        </if>
        <if test="productionPlanId != null">
            and production_plan_id = #{productionPlanId}
        </if>
        <if test="semiProductId != null">
            and semi_product_id = #{semiProductId}
        </if>
        <if test="productId != null">
            and product_id = #{productId}
        </if>
        <if test="cropType != null and cropType != ''">
            and crop_type = #{cropType}
        </if>
        <if test="cropName != null and cropName != ''">
            and crop_name = #{cropName}
        </if>
        <if test="scrapQuantity != null">
            and scrap_quantity = #{scrapQuantity}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_product_scrap
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_product_scrap
         where scrap_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_product_scrap
         where scrap_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_product_scrap
         where scrap_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_product_scrap
         where scrap_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.scrapId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_product_scrap
         where scrap_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_product_scrap
         where scrap_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_product_scrap
         where scrap_id = #{scrapId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_product_scrap
         where scrap_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.scrapId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="scrapId">
        insert into ns_product_scrap
        <trim prefix="(" suffix=")" suffixOverrides="," >
            scrap_id,
            applicant,
            applicant_name,
            business_type,
            warehouse_id,
            warehouse_code,
            order_id,
            production_plan_id,
            semi_product_id,
            product_id,
            crop_type,
            crop_name,
            scrap_quantity,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{scrapId},
            #{applicant},
            #{applicantName},
            #{businessType},
            #{warehouseId},
            #{warehouseCode},
            #{orderId},
            #{productionPlanId},
            #{semiProductId},
            #{productId},
            #{cropType},
            #{cropName},
            #{scrapQuantity},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{regionDeptId}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="scrapId">
        insert into ns_product_scrap
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="scrapId != null" >
                scrap_id,
            </if>
            <if test="applicant != null" >
                applicant,
            </if>
            <if test="applicantName != null" >
                applicant_name,
            </if>
            <if test="businessType != null" >
                business_type,
            </if>
            <if test="warehouseId != null" >
                warehouse_id,
            </if>
            <if test="warehouseCode != null" >
                warehouse_code,
            </if>
            <if test="orderId != null" >
                order_id,
            </if>
            <if test="productionPlanId != null" >
                production_plan_id,
            </if>
            <if test="semiProductId != null" >
                semi_product_id,
            </if>
            <if test="productId != null" >
                product_id,
            </if>
            <if test="cropType != null" >
                crop_type,
            </if>
            <if test="cropName != null" >
                crop_name,
            </if>
            <if test="scrapQuantity != null" >
                scrap_quantity,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="scrapId != null" >
                #{scrapId},
            </if>
            <if test="applicant != null" >
                #{applicant},
            </if>
            <if test="applicantName != null" >
                #{applicantName},
            </if>
            <if test="businessType != null" >
                #{businessType},
            </if>
            <if test="warehouseId != null" >
                #{warehouseId},
            </if>
            <if test="warehouseCode != null" >
                #{warehouseCode},
            </if>
            <if test="orderId != null" >
                #{orderId},
            </if>
            <if test="productionPlanId != null" >
                #{productionPlanId},
            </if>
            <if test="semiProductId != null" >
                #{semiProductId},
            </if>
            <if test="productId != null" >
                #{productId},
            </if>
            <if test="cropType != null" >
                #{cropType},
            </if>
            <if test="cropName != null" >
                #{cropName},
            </if>
            <if test="scrapQuantity != null" >
                #{scrapQuantity},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" useGeneratedKeys="true" keyProperty="scrapId">
        insert into ns_product_scrap
        <trim prefix="(" suffix=")" suffixOverrides="," >
            scrap_id,
            applicant,
            applicant_name,
            business_type,
            warehouse_id,
            warehouse_code,
            order_id,
            production_plan_id,
            semi_product_id,
            product_id,
            crop_type,
            crop_name,
            scrap_quantity,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.scrapId},
                #{item.applicant},
                #{item.applicantName},
                #{item.businessType},
                #{item.warehouseId},
                #{item.warehouseCode},
                #{item.orderId},
                #{item.productionPlanId},
                #{item.semiProductId},
                #{item.productId},
                #{item.cropType},
                #{item.cropName},
                #{item.scrapQuantity},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.regionDeptId}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_product_scrap
          <set>
            <if test="applicant != null" >
                applicant=#{applicant},
            </if>
            <if test="applicantName != null" >
                applicant_name=#{applicantName},
            </if>
            <if test="businessType != null" >
                business_type=#{businessType},
            </if>
            <if test="warehouseId != null" >
                warehouse_id=#{warehouseId},
            </if>
            <if test="warehouseCode != null" >
                warehouse_code=#{warehouseCode},
            </if>
            <if test="orderId != null" >
                order_id=#{orderId},
            </if>
            <if test="productionPlanId != null" >
                production_plan_id=#{productionPlanId},
            </if>
            <if test="semiProductId != null" >
                semi_product_id=#{semiProductId},
            </if>
            <if test="productId != null" >
                product_id=#{productId},
            </if>
            <if test="cropType != null" >
                crop_type=#{cropType},
            </if>
            <if test="cropName != null" >
                crop_name=#{cropName},
            </if>
            <if test="scrapQuantity != null" >
                scrap_quantity=#{scrapQuantity},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
          </set>
         where scrap_id = #{scrapId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="scrapId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="scrapId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_product_scrap
          <set>
                applicant=#{applicant},
                applicant_name=#{applicantName},
                business_type=#{businessType},
                warehouse_id=#{warehouseId},
                warehouse_code=#{warehouseCode},
                order_id=#{orderId},
                production_plan_id=#{productionPlanId},
                semi_product_id=#{semiProductId},
                product_id=#{productId},
                crop_type=#{cropType},
                crop_name=#{cropName},
                scrap_quantity=#{scrapQuantity},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                region_dept_id=#{regionDeptId},
          </set>
         where scrap_id = #{scrapId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="scrapId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="scrapId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
