<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_approval_process (审批流程表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsApprovalProcessDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsApprovalProcess">
        <id column="approval_id" property="approvalId" />
        <result column="initiator" property="initiator" />
        <result column="initiator_id" property="initiatorId" />
        <result column="initiation_time" property="initiationTime" />
        <result column="business_type" property="businessType" />
        <result column="business_id" property="businessId" />
        <result column="approval_content" property="approvalContent" />
        <result column="approval_status" property="approvalStatus" />
        <result column="approver" property="approver" />
        <result column="approved_by" property="approvedBy" />
        <result column="approved_time" property="approvedTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        approval_id,
        initiator,
        initiator_id,
        initiation_time,
        business_type,
        business_id,
        approval_content,
        approval_status,
        approver,
        approved_by,
        approved_time,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="approvalId != null">
            and approval_id = #{approvalId}
        </if>
        <if test="initiator != null and initiator != ''">
            and initiator = #{initiator}
        </if>
        <if test="initiatorId != null">
            and initiator_id = #{initiatorId}
        </if>
        <if test="initiationTime != null">
            and initiation_time = #{initiationTime}
        </if>
        <if test="businessType != null and businessType != ''">
            and business_type = #{businessType}
        </if>
        <if test="businessId != null">
            and business_id = #{businessId}
        </if>
        <if test="approvalContent != null and approvalContent != ''">
            and approval_content = #{approvalContent}
        </if>
        <if test="approvalStatus != null">
            and approval_status = #{approvalStatus}
        </if>
        <if test="approver != null and approver != ''">
            and approver = #{approver}
        </if>
        <if test="approvedBy != null">
            and approved_by = #{approvedBy}
        </if>
        <if test="approvedTime != null">
            and approved_time = #{approvedTime}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_approval_process
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_approval_process
         where approval_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_approval_process
         where approval_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_approval_process
         where approval_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_approval_process
         where approval_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.approvalId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_approval_process
         where approval_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_approval_process
         where approval_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_approval_process
         where approval_id = #{approvalId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_approval_process
         where approval_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.approvalId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="approvalId">
        insert into ns_approval_process
        <trim prefix="(" suffix=")" suffixOverrides="," >
            approval_id,
            initiator,
            initiator_id,
            initiation_time,
            business_type,
            business_id,
            approval_content,
            approval_status,
            approver,
            approved_by,
            approved_time,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{approvalId},
            #{initiator},
            #{initiatorId},
            #{initiationTime},
            #{businessType},
            #{businessId},
            #{approvalContent},
            #{approvalStatus},
            #{approver},
            #{approvedBy},
            #{approvedTime},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="approvalId">
        insert into ns_approval_process
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="approvalId != null" >
                approval_id,
            </if>
            <if test="initiator != null" >
                initiator,
            </if>
            <if test="initiatorId != null" >
                initiator_id,
            </if>
            <if test="initiationTime != null" >
                initiation_time,
            </if>
            <if test="businessType != null" >
                business_type,
            </if>
            <if test="businessId != null" >
                business_id,
            </if>
            <if test="approvalContent != null" >
                approval_content,
            </if>
            <if test="approvalStatus != null" >
                approval_status,
            </if>
            <if test="approver != null" >
                approver,
            </if>
            <if test="approvedBy != null" >
                approved_by,
            </if>
            <if test="approvedTime != null" >
                approved_time,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="approvalId != null" >
                #{approvalId},
            </if>
            <if test="initiator != null" >
                #{initiator},
            </if>
            <if test="initiatorId != null" >
                #{initiatorId},
            </if>
            <if test="initiationTime != null" >
                #{initiationTime},
            </if>
            <if test="businessType != null" >
                #{businessType},
            </if>
            <if test="businessId != null" >
                #{businessId},
            </if>
            <if test="approvalContent != null" >
                #{approvalContent},
            </if>
            <if test="approvalStatus != null" >
                #{approvalStatus},
            </if>
            <if test="approver != null" >
                #{approver},
            </if>
            <if test="approvedBy != null" >
                #{approvedBy},
            </if>
            <if test="approvedTime != null" >
                #{approvedTime},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" useGeneratedKeys="true" keyProperty="approvalId">
        insert into ns_approval_process
        <trim prefix="(" suffix=")" suffixOverrides="," >
            approval_id,
            initiator,
            initiator_id,
            initiation_time,
            business_type,
            business_id,
            approval_content,
            approval_status,
            approver,
            approved_by,
            approved_time,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.approvalId},
                #{item.initiator},
                #{item.initiatorId},
                #{item.initiationTime},
                #{item.businessType},
                #{item.businessId},
                #{item.approvalContent},
                #{item.approvalStatus},
                #{item.approver},
                #{item.approvedBy},
                #{item.approvedTime},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_approval_process
          <set>
            <if test="initiator != null" >
                initiator=#{initiator},
            </if>
            <if test="initiatorId != null" >
                initiator_id=#{initiatorId},
            </if>
            <if test="initiationTime != null" >
                initiation_time=#{initiationTime},
            </if>
            <if test="businessType != null" >
                business_type=#{businessType},
            </if>
            <if test="businessId != null" >
                business_id=#{businessId},
            </if>
            <if test="approvalContent != null" >
                approval_content=#{approvalContent},
            </if>
            <if test="approvalStatus != null" >
                approval_status=#{approvalStatus},
            </if>
            <if test="approver != null" >
                approver=#{approver},
            </if>
            <if test="approvedBy != null" >
                approved_by=#{approvedBy},
            </if>
            <if test="approvedTime != null" >
                approved_time=#{approvedTime},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
          </set>
         where approval_id = #{approvalId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="approvalId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="approvalId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_approval_process
          <set>
                initiator=#{initiator},
                initiator_id=#{initiatorId},
                initiation_time=#{initiationTime},
                business_type=#{businessType},
                business_id=#{businessId},
                approval_content=#{approvalContent},
                approval_status=#{approvalStatus},
                approver=#{approver},
                approved_by=#{approvedBy},
                approved_time=#{approvedTime},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
          </set>
         where approval_id = #{approvalId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="approvalId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="approvalId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
