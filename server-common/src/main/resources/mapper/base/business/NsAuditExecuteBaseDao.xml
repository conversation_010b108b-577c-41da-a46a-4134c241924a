<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_audit_execute (稽核执行)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsAuditExecuteDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsAuditExecute">
        <id column="audit_id" property="auditId" />
        <result column="audit_total" property="auditTotal" />
        <result column="audit_name" property="auditName" />
        <result column="audit_remarks" property="auditRemarks" />
        <result column="planting_area" property="plantingArea" />
        <result column="house_name" property="houseName" />
        <result column="execute_time" property="executeTime" />
        <result column="technical_personnel" property="technicalPersonnel" />
        <result column="technical_personnel_id" property="technicalPersonnelId" />
        <result column="sending_level" property="sendingLevel" />
        <result column="picture_url" property="pictureUrl" />
        <result column="deleted" property="deleted" />
        <result column="delay" property="delay" />
        <result column="current_statu" property="currentStatu" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="region_dept_id" property="regionDeptId" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        audit_id,
        audit_total,
        audit_name,
        audit_remarks,
        planting_area,
        house_name,
        execute_time,
        technical_personnel,
        technical_personnel_id,
        sending_level,
        picture_url,
        deleted,
        delay,
        current_statu,
        create_by,
        create_time,
        update_by,
        update_time,
        region_dept_id
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="auditId != null">
            and audit_id = #{auditId}
        </if>
        <if test="auditTotal != null and auditTotal != ''">
            and audit_total = #{auditTotal}
        </if>
        <if test="auditName != null and auditName != ''">
            and audit_name = #{auditName}
        </if>
        <if test="auditRemarks != null and auditRemarks != ''">
            and audit_remarks = #{auditRemarks}
        </if>
        <if test="plantingArea != null and plantingArea != ''">
            and planting_area = #{plantingArea}
        </if>
        <if test="houseName != null and houseName != ''">
            and house_name = #{houseName}
        </if>
        <if test="executeTime != null">
            and execute_time = #{executeTime}
        </if>
        <if test="technicalPersonnel != null and technicalPersonnel != ''">
            and technical_personnel = #{technicalPersonnel}
        </if>
        <if test="technicalPersonnelId != null">
            and technical_personnel_id = #{technicalPersonnelId}
        </if>
        <if test="sendingLevel != null and sendingLevel != ''">
            and sending_level = #{sendingLevel}
        </if>
        <if test="pictureUrl != null and pictureUrl != ''">
            and picture_url = #{pictureUrl}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="delay != null">
            and delay = #{delay}
        </if>
        <if test="currentStatu != null">
            and current_statu = #{currentStatu}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_audit_execute
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_audit_execute
         where audit_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_audit_execute
         where audit_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_audit_execute
         where audit_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_audit_execute
         where audit_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.auditId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_audit_execute
         where audit_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_audit_execute
         where audit_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_audit_execute
         where audit_id = #{auditId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_audit_execute
         where audit_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.auditId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_audit_execute
        <trim prefix="(" suffix=")" suffixOverrides="," >
            audit_id,
            audit_total,
            audit_name,
            audit_remarks,
            planting_area,
            house_name,
            execute_time,
            technical_personnel,
            technical_personnel_id,
            sending_level,
            picture_url,
            deleted,
            delay,
            current_statu,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{auditId},
            #{auditTotal},
            #{auditName},
            #{auditRemarks},
            #{plantingArea},
            #{houseName},
            #{executeTime},
            #{technicalPersonnel},
            #{technicalPersonnelId},
            #{sendingLevel},
            #{pictureUrl},
            #{deleted},
            #{delay},
            #{currentStatu},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{regionDeptId}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_audit_execute
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="auditId != null" >
                audit_id,
            </if>
            <if test="auditTotal != null" >
                audit_total,
            </if>
            <if test="auditName != null" >
                audit_name,
            </if>
            <if test="auditRemarks != null" >
                audit_remarks,
            </if>
            <if test="plantingArea != null" >
                planting_area,
            </if>
            <if test="houseName != null" >
                house_name,
            </if>
            <if test="executeTime != null" >
                execute_time,
            </if>
            <if test="technicalPersonnel != null" >
                technical_personnel,
            </if>
            <if test="technicalPersonnelId != null" >
                technical_personnel_id,
            </if>
            <if test="sendingLevel != null" >
                sending_level,
            </if>
            <if test="pictureUrl != null" >
                picture_url,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="delay != null" >
                delay,
            </if>
            <if test="currentStatu != null" >
                current_statu,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="auditId != null" >
                #{auditId},
            </if>
            <if test="auditTotal != null" >
                #{auditTotal},
            </if>
            <if test="auditName != null" >
                #{auditName},
            </if>
            <if test="auditRemarks != null" >
                #{auditRemarks},
            </if>
            <if test="plantingArea != null" >
                #{plantingArea},
            </if>
            <if test="houseName != null" >
                #{houseName},
            </if>
            <if test="executeTime != null" >
                #{executeTime},
            </if>
            <if test="technicalPersonnel != null" >
                #{technicalPersonnel},
            </if>
            <if test="technicalPersonnelId != null" >
                #{technicalPersonnelId},
            </if>
            <if test="sendingLevel != null" >
                #{sendingLevel},
            </if>
            <if test="pictureUrl != null" >
                #{pictureUrl},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="delay != null" >
                #{delay},
            </if>
            <if test="currentStatu != null" >
                #{currentStatu},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_audit_execute
        <trim prefix="(" suffix=")" suffixOverrides="," >
            audit_id,
            audit_total,
            audit_name,
            audit_remarks,
            planting_area,
            house_name,
            execute_time,
            technical_personnel,
            technical_personnel_id,
            sending_level,
            picture_url,
            deleted,
            delay,
            current_statu,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.auditId},
                #{item.auditTotal},
                #{item.auditName},
                #{item.auditRemarks},
                #{item.plantingArea},
                #{item.houseName},
                #{item.executeTime},
                #{item.technicalPersonnel},
                #{item.technicalPersonnelId},
                #{item.sendingLevel},
                #{item.pictureUrl},
                #{item.deleted},
                #{item.delay},
                #{item.currentStatu},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.regionDeptId}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_audit_execute
          <set>
            <if test="auditTotal != null" >
                audit_total=#{auditTotal},
            </if>
            <if test="auditName != null" >
                audit_name=#{auditName},
            </if>
            <if test="auditRemarks != null" >
                audit_remarks=#{auditRemarks},
            </if>
            <if test="plantingArea != null" >
                planting_area=#{plantingArea},
            </if>
            <if test="houseName != null" >
                house_name=#{houseName},
            </if>
            <if test="executeTime != null" >
                execute_time=#{executeTime},
            </if>
            <if test="technicalPersonnel != null" >
                technical_personnel=#{technicalPersonnel},
            </if>
            <if test="technicalPersonnelId != null" >
                technical_personnel_id=#{technicalPersonnelId},
            </if>
            <if test="sendingLevel != null" >
                sending_level=#{sendingLevel},
            </if>
            <if test="pictureUrl != null" >
                picture_url=#{pictureUrl},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="delay != null" >
                delay=#{delay},
            </if>
            <if test="currentStatu != null" >
                current_statu=#{currentStatu},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
          </set>
         where audit_id = #{auditId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_audit_execute
          <set>
                audit_total=#{auditTotal},
                audit_name=#{auditName},
                audit_remarks=#{auditRemarks},
                planting_area=#{plantingArea},
                house_name=#{houseName},
                execute_time=#{executeTime},
                technical_personnel=#{technicalPersonnel},
                technical_personnel_id=#{technicalPersonnelId},
                sending_level=#{sendingLevel},
                picture_url=#{pictureUrl},
                deleted=#{deleted},
                delay=#{delay},
                current_statu=#{currentStatu},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                region_dept_id=#{regionDeptId},
          </set>
         where audit_id = #{auditId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
