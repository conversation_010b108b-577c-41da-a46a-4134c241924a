<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_agricultural_materials (农资管理主表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsAgriculturalMaterialsDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsAgriculturalMaterials">
        <id column="agricultural_materials_id" property="agriculturalMaterialsId" />
        <result column="agricultural_materials_type" property="agriculturalMaterialsType" />
        <result column="brand_name" property="brandName" />
        <result column="active_ingredient" property="activeIngredient" />
        <result column="usage_method" property="usageMethod" />
        <result column="registration_no" property="registrationNo" />
        <result column="dosage_form" property="dosageForm" />
        <result column="pack_quantity" property="packQuantity" />
        <result column="pack_unit" property="packUnit" />
        <result column="stock" property="stock" />
        <result column="stock_unit" property="stockUnit" />
        <result column="stock_weight" property="stockWeight" />
        <result column="validity" property="validity" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="stock_warning_line" property="stockWarningLine" />
        <result column="region_dept_id" property="regionDeptId" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        agricultural_materials_id,
        agricultural_materials_type,
        brand_name,
        active_ingredient,
        usage_method,
        registration_no,
        dosage_form,
        pack_quantity,
        pack_unit,
        stock,
        stock_unit,
        stock_weight,
        validity,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        stock_warning_line,
        region_dept_id
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="agriculturalMaterialsId != null">
            and agricultural_materials_id = #{agriculturalMaterialsId}
        </if>
        <if test="agriculturalMaterialsType != null and agriculturalMaterialsType != ''">
            and agricultural_materials_type = #{agriculturalMaterialsType}
        </if>
        <if test="brandName != null and brandName != ''">
            and brand_name = #{brandName}
        </if>
        <if test="activeIngredient != null and activeIngredient != ''">
            and active_ingredient = #{activeIngredient}
        </if>
        <if test="usageMethod != null and usageMethod != ''">
            and usage_method = #{usageMethod}
        </if>
        <if test="registrationNo != null and registrationNo != ''">
            and registration_no = #{registrationNo}
        </if>
        <if test="dosageForm != null and dosageForm != ''">
            and dosage_form = #{dosageForm}
        </if>
        <if test="packQuantity != null">
            and pack_quantity = #{packQuantity}
        </if>
        <if test="packUnit != null and packUnit != ''">
            and pack_unit = #{packUnit}
        </if>
        <if test="stock != null">
            and stock = #{stock}
        </if>
        <if test="stockUnit != null and stockUnit != ''">
            and stock_unit = #{stockUnit}
        </if>
        <if test="stockWeight != null">
            and stock_weight = #{stockWeight}
        </if>
        <if test="validity != null">
            and validity = #{validity}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="stockWarningLine != null">
            and stock_warning_line = #{stockWarningLine}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_agricultural_materials
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_agricultural_materials
         where agricultural_materials_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_agricultural_materials
         where agricultural_materials_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_agricultural_materials
         where agricultural_materials_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_agricultural_materials
         where agricultural_materials_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.agriculturalMaterialsId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_agricultural_materials
         where agricultural_materials_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_agricultural_materials
         where agricultural_materials_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_agricultural_materials
         where agricultural_materials_id = #{agriculturalMaterialsId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_agricultural_materials
         where agricultural_materials_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.agriculturalMaterialsId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_agricultural_materials
        <trim prefix="(" suffix=")" suffixOverrides="," >
            agricultural_materials_id,
            agricultural_materials_type,
            brand_name,
            active_ingredient,
            usage_method,
            registration_no,
            dosage_form,
            pack_quantity,
            pack_unit,
            stock,
            stock_unit,
            stock_weight,
            validity,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            stock_warning_line,
            region_dept_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{agriculturalMaterialsId},
            #{agriculturalMaterialsType},
            #{brandName},
            #{activeIngredient},
            #{usageMethod},
            #{registrationNo},
            #{dosageForm},
            #{packQuantity},
            #{packUnit},
            #{stock},
            #{stockUnit},
            #{stockWeight},
            #{validity},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{stockWarningLine},
            #{regionDeptId}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_agricultural_materials
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="agriculturalMaterialsId != null" >
                agricultural_materials_id,
            </if>
            <if test="agriculturalMaterialsType != null" >
                agricultural_materials_type,
            </if>
            <if test="brandName != null" >
                brand_name,
            </if>
            <if test="activeIngredient != null" >
                active_ingredient,
            </if>
            <if test="usageMethod != null" >
                usage_method,
            </if>
            <if test="registrationNo != null" >
                registration_no,
            </if>
            <if test="dosageForm != null" >
                dosage_form,
            </if>
            <if test="packQuantity != null" >
                pack_quantity,
            </if>
            <if test="packUnit != null" >
                pack_unit,
            </if>
            <if test="stock != null" >
                stock,
            </if>
            <if test="stockUnit != null" >
                stock_unit,
            </if>
            <if test="stockWeight != null" >
                stock_weight,
            </if>
            <if test="validity != null" >
                validity,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="stockWarningLine != null" >
                stock_warning_line,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="agriculturalMaterialsId != null" >
                #{agriculturalMaterialsId},
            </if>
            <if test="agriculturalMaterialsType != null" >
                #{agriculturalMaterialsType},
            </if>
            <if test="brandName != null" >
                #{brandName},
            </if>
            <if test="activeIngredient != null" >
                #{activeIngredient},
            </if>
            <if test="usageMethod != null" >
                #{usageMethod},
            </if>
            <if test="registrationNo != null" >
                #{registrationNo},
            </if>
            <if test="dosageForm != null" >
                #{dosageForm},
            </if>
            <if test="packQuantity != null" >
                #{packQuantity},
            </if>
            <if test="packUnit != null" >
                #{packUnit},
            </if>
            <if test="stock != null" >
                #{stock},
            </if>
            <if test="stockUnit != null" >
                #{stockUnit},
            </if>
            <if test="stockWeight != null" >
                #{stockWeight},
            </if>
            <if test="validity != null" >
                #{validity},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="stockWarningLine != null" >
                #{stockWarningLine},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_agricultural_materials
        <trim prefix="(" suffix=")" suffixOverrides="," >
            agricultural_materials_id,
            agricultural_materials_type,
            brand_name,
            active_ingredient,
            usage_method,
            registration_no,
            dosage_form,
            pack_quantity,
            pack_unit,
            stock,
            stock_unit,
            stock_weight,
            validity,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            stock_warning_line,
            region_dept_id
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.agriculturalMaterialsId},
                #{item.agriculturalMaterialsType},
                #{item.brandName},
                #{item.activeIngredient},
                #{item.usageMethod},
                #{item.registrationNo},
                #{item.dosageForm},
                #{item.packQuantity},
                #{item.packUnit},
                #{item.stock},
                #{item.stockUnit},
                #{item.stockWeight},
                #{item.validity},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.stockWarningLine},
                #{item.regionDeptId}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_agricultural_materials
          <set>
            <if test="agriculturalMaterialsType != null" >
                agricultural_materials_type=#{agriculturalMaterialsType},
            </if>
            <if test="brandName != null" >
                brand_name=#{brandName},
            </if>
            <if test="activeIngredient != null" >
                active_ingredient=#{activeIngredient},
            </if>
            <if test="usageMethod != null" >
                usage_method=#{usageMethod},
            </if>
            <if test="registrationNo != null" >
                registration_no=#{registrationNo},
            </if>
            <if test="dosageForm != null" >
                dosage_form=#{dosageForm},
            </if>
            <if test="packQuantity != null" >
                pack_quantity=#{packQuantity},
            </if>
            <if test="packUnit != null" >
                pack_unit=#{packUnit},
            </if>
            <if test="stock != null" >
                stock=#{stock},
            </if>
            <if test="stockUnit != null" >
                stock_unit=#{stockUnit},
            </if>
            <if test="stockWeight != null" >
                stock_weight=#{stockWeight},
            </if>
            <if test="validity != null" >
                validity=#{validity},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="stockWarningLine != null" >
                stock_warning_line=#{stockWarningLine},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
          </set>
         where agricultural_materials_id = #{agriculturalMaterialsId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_agricultural_materials
          <set>
                agricultural_materials_type=#{agriculturalMaterialsType},
                brand_name=#{brandName},
                active_ingredient=#{activeIngredient},
                usage_method=#{usageMethod},
                registration_no=#{registrationNo},
                dosage_form=#{dosageForm},
                pack_quantity=#{packQuantity},
                pack_unit=#{packUnit},
                stock=#{stock},
                stock_unit=#{stockUnit},
                stock_weight=#{stockWeight},
                validity=#{validity},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                stock_warning_line=#{stockWarningLine},
                region_dept_id=#{regionDeptId},
          </set>
         where agricultural_materials_id = #{agriculturalMaterialsId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
