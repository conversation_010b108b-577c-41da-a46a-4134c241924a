<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_input_stock (投入品库存表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsInputStockDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsInputStock">
        <id column="input_id" property="inputId" />
        <result column="business_id" property="businessId" />
        <result column="input_type" property="inputType" />
        <result column="input_category" property="inputCategory" />
        <result column="input_sub_category" property="inputSubCategory" />
        <result column="input_name" property="inputName" />
        <result column="stock_warning_line" property="stockWarningLine" />
        <result column="stock_quantity" property="stockQuantity" />
        <result column="warning_status" property="warningStatus" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="region_dept_id" property="regionDeptId" />
        <result column="spec" property="spec" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        input_id,
        business_id,
        input_type,
        input_category,
        input_sub_category,
        input_name,
        stock_warning_line,
        stock_quantity,
        warning_status,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        region_dept_id,
        spec
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="inputId != null">
            and input_id = #{inputId}
        </if>
        <if test="businessId != null">
            and business_id = #{businessId}
        </if>
        <if test="inputType != null">
            and input_type = #{inputType}
        </if>
        <if test="inputCategory != null and inputCategory != ''">
            and input_category = #{inputCategory}
        </if>
        <if test="inputSubCategory != null and inputSubCategory != ''">
            and input_sub_category = #{inputSubCategory}
        </if>
        <if test="inputName != null and inputName != ''">
            and input_name = #{inputName}
        </if>
        <if test="stockWarningLine != null">
            and stock_warning_line = #{stockWarningLine}
        </if>
        <if test="stockQuantity != null">
            and stock_quantity = #{stockQuantity}
        </if>
        <if test="warningStatus != null and warningStatus != ''">
            and warning_status = #{warningStatus}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
        <if test="spec != null and spec != ''">
            and spec = #{spec}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_input_stock
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_input_stock
         where input_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_input_stock
         where input_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_input_stock
         where input_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_input_stock
         where input_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.inputId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_input_stock
         where input_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_input_stock
         where input_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_input_stock
         where input_id = #{inputId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_input_stock
         where input_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.inputId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="inputId">
        insert into ns_input_stock
        <trim prefix="(" suffix=")" suffixOverrides="," >
            input_id,
            business_id,
            input_type,
            input_category,
            input_sub_category,
            input_name,
            stock_warning_line,
            stock_quantity,
            warning_status,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id,
            spec
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{inputId},
            #{businessId},
            #{inputType},
            #{inputCategory},
            #{inputSubCategory},
            #{inputName},
            #{stockWarningLine},
            #{stockQuantity},
            #{warningStatus},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{regionDeptId},
            #{spec}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="inputId">
        insert into ns_input_stock
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="inputId != null" >
                input_id,
            </if>
            <if test="businessId != null" >
                business_id,
            </if>
            <if test="inputType != null" >
                input_type,
            </if>
            <if test="inputCategory != null" >
                input_category,
            </if>
            <if test="inputSubCategory != null" >
                input_sub_category,
            </if>
            <if test="inputName != null" >
                input_name,
            </if>
            <if test="stockWarningLine != null" >
                stock_warning_line,
            </if>
            <if test="stockQuantity != null" >
                stock_quantity,
            </if>
            <if test="warningStatus != null" >
                warning_status,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id,
            </if>
            <if test="spec != null" >
                spec
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="inputId != null" >
                #{inputId},
            </if>
            <if test="businessId != null" >
                #{businessId},
            </if>
            <if test="inputType != null" >
                #{inputType},
            </if>
            <if test="inputCategory != null" >
                #{inputCategory},
            </if>
            <if test="inputSubCategory != null" >
                #{inputSubCategory},
            </if>
            <if test="inputName != null" >
                #{inputName},
            </if>
            <if test="stockWarningLine != null" >
                #{stockWarningLine},
            </if>
            <if test="stockQuantity != null" >
                #{stockQuantity},
            </if>
            <if test="warningStatus != null" >
                #{warningStatus},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId},
            </if>
            <if test="spec != null" >
                #{spec}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" useGeneratedKeys="true" keyProperty="inputId">
        insert into ns_input_stock
        <trim prefix="(" suffix=")" suffixOverrides="," >
            input_id,
            business_id,
            input_type,
            input_category,
            input_sub_category,
            input_name,
            stock_warning_line,
            stock_quantity,
            warning_status,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id,
            spec
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.inputId},
                #{item.businessId},
                #{item.inputType},
                #{item.inputCategory},
                #{item.inputSubCategory},
                #{item.inputName},
                #{item.stockWarningLine},
                #{item.stockQuantity},
                #{item.warningStatus},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.regionDeptId},
                #{item.spec}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_input_stock
          <set>
            <if test="businessId != null" >
                business_id=#{businessId},
            </if>
            <if test="inputType != null" >
                input_type=#{inputType},
            </if>
            <if test="inputCategory != null" >
                input_category=#{inputCategory},
            </if>
            <if test="inputSubCategory != null" >
                input_sub_category=#{inputSubCategory},
            </if>
            <if test="inputName != null" >
                input_name=#{inputName},
            </if>
            <if test="stockWarningLine != null" >
                stock_warning_line=#{stockWarningLine},
            </if>
            <if test="stockQuantity != null" >
                stock_quantity=#{stockQuantity},
            </if>
            <if test="warningStatus != null" >
                warning_status=#{warningStatus},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
            <if test="spec != null" >
                spec=#{spec},
            </if>
          </set>
         where input_id = #{inputId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="inputId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="inputId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_input_stock
          <set>
                business_id=#{businessId},
                input_type=#{inputType},
                input_category=#{inputCategory},
                input_sub_category=#{inputSubCategory},
                input_name=#{inputName},
                stock_warning_line=#{stockWarningLine},
                stock_quantity=#{stockQuantity},
                warning_status=#{warningStatus},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                region_dept_id=#{regionDeptId},
                spec=#{spec},
          </set>
         where input_id = #{inputId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="inputId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="inputId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
