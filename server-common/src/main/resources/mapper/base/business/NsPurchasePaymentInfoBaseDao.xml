<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_purchase_payment_info (采购支付信息表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsPurchasePaymentInfoDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsPurchasePaymentInfo">
        <id column="payment_info_id" property="paymentInfoId" />
        <result column="purchase_record_id" property="purchaseRecordId" />
        <result column="supplier_id" property="supplierId" />
        <result column="supplier_name" property="supplierName" />
        <result column="supplier_bank" property="supplierBank" />
        <result column="supplier_account" property="supplierAccount" />
        <result column="payment_amount" property="paymentAmount" />
        <result column="attachment" property="attachment" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        payment_info_id,
        purchase_record_id,
        supplier_id,
        supplier_name,
        supplier_bank,
        supplier_account,
        payment_amount,
        attachment,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="paymentInfoId != null">
            and payment_info_id = #{paymentInfoId}
        </if>
        <if test="purchaseRecordId != null">
            and purchase_record_id = #{purchaseRecordId}
        </if>
        <if test="supplierId != null">
            and supplier_id = #{supplierId}
        </if>
        <if test="supplierName != null and supplierName != ''">
            and supplier_name = #{supplierName}
        </if>
        <if test="supplierBank != null and supplierBank != ''">
            and supplier_bank = #{supplierBank}
        </if>
        <if test="supplierAccount != null and supplierAccount != ''">
            and supplier_account = #{supplierAccount}
        </if>
        <if test="paymentAmount != null">
            and payment_amount = #{paymentAmount}
        </if>
        <if test="attachment != null and attachment != ''">
            and attachment = #{attachment}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_purchase_payment_info
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_purchase_payment_info
         where payment_info_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_purchase_payment_info
         where payment_info_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_purchase_payment_info
         where payment_info_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_purchase_payment_info
         where payment_info_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.paymentInfoId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_purchase_payment_info
         where payment_info_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_purchase_payment_info
         where payment_info_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_purchase_payment_info
         where payment_info_id = #{paymentInfoId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_purchase_payment_info
         where payment_info_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.paymentInfoId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="paymentInfoId">
        insert into ns_purchase_payment_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            payment_info_id,
            purchase_record_id,
            supplier_id,
            supplier_name,
            supplier_bank,
            supplier_account,
            payment_amount,
            attachment,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{paymentInfoId},
            #{purchaseRecordId},
            #{supplierId},
            #{supplierName},
            #{supplierBank},
            #{supplierAccount},
            #{paymentAmount},
            #{attachment},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="paymentInfoId">
        insert into ns_purchase_payment_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="paymentInfoId != null" >
                payment_info_id,
            </if>
            <if test="purchaseRecordId != null" >
                purchase_record_id,
            </if>
            <if test="supplierId != null" >
                supplier_id,
            </if>
            <if test="supplierName != null" >
                supplier_name,
            </if>
            <if test="supplierBank != null" >
                supplier_bank,
            </if>
            <if test="supplierAccount != null" >
                supplier_account,
            </if>
            <if test="paymentAmount != null" >
                payment_amount,
            </if>
            <if test="attachment != null" >
                attachment,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="paymentInfoId != null" >
                #{paymentInfoId},
            </if>
            <if test="purchaseRecordId != null" >
                #{purchaseRecordId},
            </if>
            <if test="supplierId != null" >
                #{supplierId},
            </if>
            <if test="supplierName != null" >
                #{supplierName},
            </if>
            <if test="supplierBank != null" >
                #{supplierBank},
            </if>
            <if test="supplierAccount != null" >
                #{supplierAccount},
            </if>
            <if test="paymentAmount != null" >
                #{paymentAmount},
            </if>
            <if test="attachment != null" >
                #{attachment},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" useGeneratedKeys="true" keyProperty="paymentInfoId">
        insert into ns_purchase_payment_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            payment_info_id,
            purchase_record_id,
            supplier_id,
            supplier_name,
            supplier_bank,
            supplier_account,
            payment_amount,
            attachment,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.paymentInfoId},
                #{item.purchaseRecordId},
                #{item.supplierId},
                #{item.supplierName},
                #{item.supplierBank},
                #{item.supplierAccount},
                #{item.paymentAmount},
                #{item.attachment},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_purchase_payment_info
          <set>
            <if test="purchaseRecordId != null" >
                purchase_record_id=#{purchaseRecordId},
            </if>
            <if test="supplierId != null" >
                supplier_id=#{supplierId},
            </if>
            <if test="supplierName != null" >
                supplier_name=#{supplierName},
            </if>
            <if test="supplierBank != null" >
                supplier_bank=#{supplierBank},
            </if>
            <if test="supplierAccount != null" >
                supplier_account=#{supplierAccount},
            </if>
            <if test="paymentAmount != null" >
                payment_amount=#{paymentAmount},
            </if>
            <if test="attachment != null" >
                attachment=#{attachment},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
          </set>
         where payment_info_id = #{paymentInfoId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="paymentInfoId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="paymentInfoId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_purchase_payment_info
          <set>
                purchase_record_id=#{purchaseRecordId},
                supplier_id=#{supplierId},
                supplier_name=#{supplierName},
                supplier_bank=#{supplierBank},
                supplier_account=#{supplierAccount},
                payment_amount=#{paymentAmount},
                attachment=#{attachment},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
          </set>
         where payment_info_id = #{paymentInfoId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="paymentInfoId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="paymentInfoId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
