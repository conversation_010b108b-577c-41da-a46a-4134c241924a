<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_house (大棚)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsHouseDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsHouse">
        <id column="house_id" property="houseId" />
        <result column="house_name" property="houseName" />
        <result column="house_region_id" property="houseRegionId" />
        <result column="house_region_name" property="houseRegionName" />
        <result column="region_dept_id" property="regionDeptId" />
        <result column="region_dept_name" property="regionDeptName" />
        <result column="house_area" property="houseArea" />
        <result column="technician_id" property="technicianId" />
        <result column="technician_name" property="technicianName" />
        <result column="technician_agent_id" property="technicianAgentId" />
        <result column="technician_agent_name" property="technicianAgentName" />
        <result column="house_points" property="housePoints" />
        <result column="is_plant" property="isPlant" />
        <result column="process_stage" property="processStage" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="image_path" property="imagePath" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        house_id,
        house_name,
        house_region_id,
        house_region_name,
        region_dept_id,
        region_dept_name,
        house_area,
        technician_id,
        technician_name,
        technician_agent_id,
        technician_agent_name,
        house_points,
        is_plant,
        process_stage,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        image_path
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="houseId != null">
            and house_id = #{houseId}
        </if>
        <if test="houseName != null and houseName != ''">
            and house_name = #{houseName}
        </if>
        <if test="houseRegionId != null">
            and house_region_id = #{houseRegionId}
        </if>
        <if test="houseRegionName != null and houseRegionName != ''">
            and house_region_name = #{houseRegionName}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
        <if test="regionDeptName != null and regionDeptName != ''">
            and region_dept_name = #{regionDeptName}
        </if>
        <if test="houseArea != null and houseArea != ''">
            and house_area = #{houseArea}
        </if>
        <if test="technicianId != null">
            and technician_id = #{technicianId}
        </if>
        <if test="technicianName != null and technicianName != ''">
            and technician_name = #{technicianName}
        </if>
        <if test="technicianAgentId != null">
            and technician_agent_id = #{technicianAgentId}
        </if>
        <if test="technicianAgentName != null and technicianAgentName != ''">
            and technician_agent_name = #{technicianAgentName}
        </if>
        <if test="housePoints != null and housePoints != ''">
            and house_points = #{housePoints}
        </if>
        <if test="isPlant != null">
            and is_plant = #{isPlant}
        </if>
        <if test="processStage != null">
            and process_stage = #{processStage}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="imagePath != null and imagePath != ''">
            and image_path = #{imagePath}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_house
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_house
         where house_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_house
         where house_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_house
         where house_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_house
         where house_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.houseId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_house
         where house_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_house
         where house_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_house
         where house_id = #{houseId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_house
         where house_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.houseId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_house
        <trim prefix="(" suffix=")" suffixOverrides="," >
            house_id,
            house_name,
            house_region_id,
            house_region_name,
            region_dept_id,
            region_dept_name,
            house_area,
            technician_id,
            technician_name,
            technician_agent_id,
            technician_agent_name,
            house_points,
            is_plant,
            process_stage,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            image_path
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{houseId},
            #{houseName},
            #{houseRegionId},
            #{houseRegionName},
            #{regionDeptId},
            #{regionDeptName},
            #{houseArea},
            #{technicianId},
            #{technicianName},
            #{technicianAgentId},
            #{technicianAgentName},
            #{housePoints},
            #{isPlant},
            #{processStage},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{imagePath}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_house
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="houseId != null" >
                house_id,
            </if>
            <if test="houseName != null" >
                house_name,
            </if>
            <if test="houseRegionId != null" >
                house_region_id,
            </if>
            <if test="houseRegionName != null" >
                house_region_name,
            </if>
            <if test="regionDeptId != null">
                region_dept_id,
            </if>
            <if test="regionDeptName != null">
                region_dept_name,
            </if>
            <if test="houseArea != null" >
                house_area,
            </if>
            <if test="technicianId != null" >
                technician_id,
            </if>
            <if test="technicianName != null" >
                technician_name,
            </if>
            <if test="technicianAgentId != null" >
                technician_agent_id,
            </if>
            <if test="technicianAgentName != null" >
                technician_agent_name,
            </if>
            <if test="housePoints != null" >
                house_points,
            </if>
            <if test="isPlant != null" >
                is_plant,
            </if>
            <if test="processStage != null" >
                process_stage,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="imagePath != null" >
                image_path
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="houseId != null" >
                #{houseId},
            </if>
            <if test="houseName != null" >
                #{houseName},
            </if>
            <if test="houseRegionId != null" >
                #{houseRegionId},
            </if>
            <if test="houseRegionName != null" >
                #{houseRegionName},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId},
            </if>
            <if test="regionDeptName != null" >
                #{regionDeptName},
            </if>
            <if test="houseArea != null" >
                #{houseArea},
            </if>
            <if test="technicianId != null" >
                #{technicianId},
            </if>
            <if test="technicianName != null" >
                #{technicianName},
            </if>
            <if test="technicianAgentId != null" >
                #{technicianAgentId},
            </if>
            <if test="technicianAgentName != null" >
                #{technicianAgentName},
            </if>
            <if test="housePoints != null" >
                #{housePoints},
            </if>
            <if test="isPlant != null" >
                #{isPlant},
            </if>
            <if test="processStage != null" >
                #{processStage},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="imagePath != null" >
                #{imagePath}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_house
        <trim prefix="(" suffix=")" suffixOverrides="," >
            house_id,
            house_name,
            house_region_id,
            house_region_name,
            region_dept_id,
            region_dept_name,
            house_area,
            technician_id,
            technician_name,
            technician_agent_id,
            technician_agent_name,
            house_points,
            is_plant,
            process_stage,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            image_path
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.houseId},
                #{item.houseName},
                #{item.houseRegionId},
                #{item.houseRegionName},
                #{item.regionDeptId},
                #{item.regionDeptName},
                #{item.houseArea},
                #{item.technicianId},
                #{item.technicianName},
                #{item.technicianAgentId},
                #{item.technicianAgentName},
                #{item.housePoints},
                #{item.isPlant},
                #{item.processStage},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.imagePath}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_house
          <set>
            <if test="houseName != null" >
                house_name=#{houseName},
            </if>
            <if test="houseRegionId != null" >
                house_region_id=#{houseRegionId},
            </if>
            <if test="houseRegionName != null" >
                house_region_name=#{houseRegionName},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
            <if test="regionDeptName != null" >
                region_dept_name=#{regionDeptName},
            </if>
            <if test="houseArea != null" >
                house_area=#{houseArea},
            </if>
            <if test="technicianId != null" >
                technician_id=#{technicianId},
            </if>
            <if test="technicianName != null" >
                technician_name=#{technicianName},
            </if>
            <if test="technicianAgentId != null" >
                technician_agent_id=#{technicianAgentId},
            </if>
            <if test="technicianAgentName != null" >
                technician_agent_name=#{technicianAgentName},
            </if>
            <if test="housePoints != null" >
                house_points=#{housePoints},
            </if>
            <if test="isPlant != null" >
                is_plant=#{isPlant},
            </if>
            <if test="processStage != null" >
                process_stage=#{processStage},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="imagePath != null" >
                image_path=#{imagePath},
            </if>
          </set>
         where house_id = #{houseId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_house
          <set>
                house_name=#{houseName},
                house_region_id=#{houseRegionId},
                house_region_name=#{houseRegionName},
                region_dept_id=#{regionDeptId},
                region_dept_name=#{regionDeptName},
                house_area=#{houseArea},
                technician_id=#{technicianId},
                technician_name=#{technicianName},
                technician_agent_id=#{technicianAgentId},
                technician_agent_name=#{technicianAgentName},
                house_points=#{housePoints},
                is_plant=#{isPlant},
                process_stage=#{processStage},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                image_path=#{imagePath},
          </set>
         where house_id = #{houseId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
