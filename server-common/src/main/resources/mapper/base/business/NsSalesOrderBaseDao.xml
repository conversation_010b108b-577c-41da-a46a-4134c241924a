<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_sales_order (销售订单表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsSalesOrderDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsSalesOrder">
        <id column="order_id" property="orderId" />
        <result column="sales_order_code" property="salesOrderCode" />
        <result column="applicant" property="applicant" />
        <result column="application_date" property="applicationDate" />
        <result column="applicant_mobile" property="applicantMobile" />
        <result column="applicant_id" property="applicantId" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="application_reason" property="applicationReason" />
        <result column="application_status" property="applicationStatus" />
        <result column="approved_by" property="approvedBy" />
        <result column="approved_time" property="approvedTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="region_dept_id" property="regionDeptId" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        order_id,
        sales_order_code,
        applicant,
        application_date,
        applicant_mobile,
        applicant_id,
        customer_id,
        customer_name,
        application_reason,
        application_status,
        approved_by,
        approved_time,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        region_dept_id
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="orderId != null">
            and order_id = #{orderId}
        </if>
        <if test="salesOrderCode != null and salesOrderCode != ''">
            and sales_order_code = #{salesOrderCode}
        </if>
        <if test="applicant != null and applicant != ''">
            and applicant = #{applicant}
        </if>
        <if test="applicationDate != null">
            and application_date = #{applicationDate}
        </if>
        <if test="applicantMobile != null and applicantMobile != ''">
            and applicant_mobile = #{applicantMobile}
        </if>
        <if test="applicantId != null">
            and applicant_id = #{applicantId}
        </if>
        <if test="customerId != null">
            and customer_id = #{customerId}
        </if>
        <if test="customerName != null and customerName != ''">
            and customer_name = #{customerName}
        </if>
        <if test="applicationReason != null and applicationReason != ''">
            and application_reason = #{applicationReason}
        </if>
        <if test="applicationStatus != null">
            and application_status = #{applicationStatus}
        </if>
        <if test="approvedBy != null">
            and approved_by = #{approvedBy}
        </if>
        <if test="approvedTime != null">
            and approved_time = #{approvedTime}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_sales_order
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sales_order
         where order_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_sales_order
         where order_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sales_order
         where order_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sales_order
         where order_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.orderId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_sales_order
         where order_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_sales_order
         where order_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_sales_order
         where order_id = #{orderId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_sales_order
         where order_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.orderId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="orderId">
        insert into ns_sales_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            order_id,
            sales_order_code,
            applicant,
            application_date,
            applicant_mobile,
            applicant_id,
            customer_id,
            customer_name,
            application_reason,
            application_status,
            approved_by,
            approved_time,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{orderId},
            #{salesOrderCode},
            #{applicant},
            #{applicationDate},
            #{applicantMobile},
            #{applicantId},
            #{customerId},
            #{customerName},
            #{applicationReason},
            #{applicationStatus},
            #{approvedBy},
            #{approvedTime},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{regionDeptId}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="orderId">
        insert into ns_sales_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orderId != null" >
                order_id,
            </if>
            <if test="salesOrderCode != null" >
                sales_order_code,
            </if>
            <if test="applicant != null" >
                applicant,
            </if>
            <if test="applicationDate != null" >
                application_date,
            </if>
            <if test="applicantMobile != null" >
                applicant_mobile,
            </if>
            <if test="applicantId != null" >
                applicant_id,
            </if>
            <if test="customerId != null" >
                customer_id,
            </if>
            <if test="customerName != null" >
                customer_name,
            </if>
            <if test="applicationReason != null" >
                application_reason,
            </if>
            <if test="applicationStatus != null" >
                application_status,
            </if>
            <if test="approvedBy != null" >
                approved_by,
            </if>
            <if test="approvedTime != null" >
                approved_time,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="orderId != null" >
                #{orderId},
            </if>
            <if test="salesOrderCode != null" >
                #{salesOrderCode},
            </if>
            <if test="applicant != null" >
                #{applicant},
            </if>
            <if test="applicationDate != null" >
                #{applicationDate},
            </if>
            <if test="applicantMobile != null" >
                #{applicantMobile},
            </if>
            <if test="applicantId != null" >
                #{applicantId},
            </if>
            <if test="customerId != null" >
                #{customerId},
            </if>
            <if test="customerName != null" >
                #{customerName},
            </if>
            <if test="applicationReason != null" >
                #{applicationReason},
            </if>
            <if test="applicationStatus != null" >
                #{applicationStatus},
            </if>
            <if test="approvedBy != null" >
                #{approvedBy},
            </if>
            <if test="approvedTime != null" >
                #{approvedTime},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" useGeneratedKeys="true" keyProperty="orderId">
        insert into ns_sales_order
        <trim prefix="(" suffix=")" suffixOverrides="," >
            order_id,
            sales_order_code,
            applicant,
            application_date,
            applicant_mobile,
            applicant_id,
            customer_id,
            customer_name,
            application_reason,
            application_status,
            approved_by,
            approved_time,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.orderId},
                #{item.salesOrderCode},
                #{item.applicant},
                #{item.applicationDate},
                #{item.applicantMobile},
                #{item.applicantId},
                #{item.customerId},
                #{item.customerName},
                #{item.applicationReason},
                #{item.applicationStatus},
                #{item.approvedBy},
                #{item.approvedTime},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.regionDeptId}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_sales_order
          <set>
            <if test="salesOrderCode != null" >
                sales_order_code=#{salesOrderCode},
            </if>
            <if test="applicant != null" >
                applicant=#{applicant},
            </if>
            <if test="applicationDate != null" >
                application_date=#{applicationDate},
            </if>
            <if test="applicantMobile != null" >
                applicant_mobile=#{applicantMobile},
            </if>
            <if test="applicantId != null" >
                applicant_id=#{applicantId},
            </if>
            <if test="customerId != null" >
                customer_id=#{customerId},
            </if>
            <if test="customerName != null" >
                customer_name=#{customerName},
            </if>
            <if test="applicationReason != null" >
                application_reason=#{applicationReason},
            </if>
            <if test="applicationStatus != null" >
                application_status=#{applicationStatus},
            </if>
            <if test="approvedBy != null" >
                approved_by=#{approvedBy},
            </if>
            <if test="approvedTime != null" >
                approved_time=#{approvedTime},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
          </set>
         where order_id = #{orderId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="orderId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="orderId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_sales_order
          <set>
                sales_order_code=#{salesOrderCode},
                applicant=#{applicant},
                application_date=#{applicationDate},
                applicant_mobile=#{applicantMobile},
                applicant_id=#{applicantId},
                customer_id=#{customerId},
                customer_name=#{customerName},
                application_reason=#{applicationReason},
                application_status=#{applicationStatus},
                approved_by=#{approvedBy},
                approved_time=#{approvedTime},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                region_dept_id=#{regionDeptId},
          </set>
         where order_id = #{orderId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="orderId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="orderId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
