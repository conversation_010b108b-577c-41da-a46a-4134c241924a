<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_planting_materials (种植项物料表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsPlantingMaterialsDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsPlantingMaterials">
        <id column="planting_materials_id" property="plantingMaterialsId" />
        <result column="planting_clause_id" property="plantingClauseId" />
        <result column="brand_name" property="brandName" />
        <result column="active_ingredient" property="activeIngredient" />
        <result column="ratio" property="ratio" />
        <result column="use_method" property="useMethod" />
        <result column="remark" property="remark" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        planting_materials_id,
        planting_clause_id,
        brand_name,
        active_ingredient,
        ratio,
        use_method,
        remark,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="plantingMaterialsId != null">
            and planting_materials_id = #{plantingMaterialsId}
        </if>
        <if test="plantingClauseId != null">
            and planting_clause_id = #{plantingClauseId}
        </if>
        <if test="brandName != null and brandName != ''">
            and brand_name = #{brandName}
        </if>
        <if test="activeIngredient != null and activeIngredient != ''">
            and active_ingredient = #{activeIngredient}
        </if>
        <if test="ratio != null">
            and ratio = #{ratio}
        </if>
        <if test="useMethod != null and useMethod != ''">
            and use_method = #{useMethod}
        </if>
        <if test="remark != null and remark != ''">
            and remark = #{remark}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_planting_materials
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_planting_materials
         where planting_materials_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_planting_materials
         where planting_materials_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_planting_materials
         where planting_materials_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_planting_materials
         where planting_materials_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.plantingMaterialsId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_planting_materials
         where planting_materials_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_planting_materials
         where planting_materials_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_planting_materials
         where planting_materials_id = #{plantingMaterialsId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_planting_materials
         where planting_materials_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.plantingMaterialsId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_planting_materials
        <trim prefix="(" suffix=")" suffixOverrides="," >
            planting_materials_id,
            planting_clause_id,
            brand_name,
            active_ingredient,
            ratio,
            use_method,
            remark,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{plantingMaterialsId},
            #{plantingClauseId},
            #{brandName},
            #{activeIngredient},
            #{ratio},
            #{useMethod},
            #{remark},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_planting_materials
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="plantingMaterialsId != null" >
                planting_materials_id,
            </if>
            <if test="plantingClauseId != null" >
                planting_clause_id,
            </if>
            <if test="brandName != null" >
                brand_name,
            </if>
            <if test="activeIngredient != null" >
                active_ingredient,
            </if>
            <if test="ratio != null" >
                ratio,
            </if>
            <if test="useMethod != null" >
                use_method,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="plantingMaterialsId != null" >
                #{plantingMaterialsId},
            </if>
            <if test="plantingClauseId != null" >
                #{plantingClauseId},
            </if>
            <if test="brandName != null" >
                #{brandName},
            </if>
            <if test="activeIngredient != null" >
                #{activeIngredient},
            </if>
            <if test="ratio != null" >
                #{ratio},
            </if>
            <if test="useMethod != null" >
                #{useMethod},
            </if>
            <if test="remark != null" >
                #{remark},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_planting_materials
        <trim prefix="(" suffix=")" suffixOverrides="," >
            planting_materials_id,
            planting_clause_id,
            brand_name,
            active_ingredient,
            ratio,
            use_method,
            remark,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.plantingMaterialsId},
                #{item.plantingClauseId},
                #{item.brandName},
                #{item.activeIngredient},
                #{item.ratio},
                #{item.useMethod},
                #{item.remark},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_planting_materials
          <set>
            <if test="plantingClauseId != null" >
                planting_clause_id=#{plantingClauseId},
            </if>
            <if test="brandName != null" >
                brand_name=#{brandName},
            </if>
            <if test="activeIngredient != null" >
                active_ingredient=#{activeIngredient},
            </if>
            <if test="ratio != null" >
                ratio=#{ratio},
            </if>
            <if test="useMethod != null" >
                use_method=#{useMethod},
            </if>
            <if test="remark != null" >
                remark=#{remark},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
          </set>
         where planting_materials_id = #{plantingMaterialsId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_planting_materials
          <set>
                planting_clause_id=#{plantingClauseId},
                brand_name=#{brandName},
                active_ingredient=#{activeIngredient},
                ratio=#{ratio},
                use_method=#{useMethod},
                remark=#{remark},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
          </set>
         where planting_materials_id = #{plantingMaterialsId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
