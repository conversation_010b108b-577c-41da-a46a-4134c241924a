<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_spec (规格表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsSpecDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsSpec">
        <id column="spec_id" property="specId" />
        <result column="spec_type" property="specType" />
        <result column="input_type" property="inputType" />
        <result column="product_stock_id" property="productStockId" />
        <result column="name" property="name" />
        <result column="unit" property="unit" />
        <result column="package_unit" property="packageUnit" />
        <result column="mark" property="mark" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="region_dept_id" property="regionDeptId" />
        <result column="num" property="num" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        spec_id,
        spec_type,
        input_type,
        product_stock_id,
        name,
        unit,
        package_unit,
        mark,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        region_dept_id,
        num
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="specId != null">
            and spec_id = #{specId}
        </if>
        <if test="specType != null">
            and spec_type = #{specType}
        </if>
        <if test="inputType != null">
            and input_type = #{inputType}
        </if>
        <if test="productStockId != null">
            and product_stock_id = #{productStockId}
        </if>
        <if test="name != null and name != ''">
            and name = #{name}
        </if>
        <if test="unit != null and unit != ''">
            and unit = #{unit}
        </if>
        <if test="packageUnit != null and packageUnit != ''">
            and package_unit = #{packageUnit}
        </if>
        <if test="mark != null and mark != ''">
            and mark = #{mark}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
        <if test="num != null">
            and num = #{num}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_spec
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_spec
         where spec_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_spec
         where spec_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_spec
         where spec_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_spec
         where spec_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.specId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_spec
         where spec_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_spec
         where spec_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_spec
         where spec_id = #{specId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_spec
         where spec_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.specId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_spec
        <trim prefix="(" suffix=")" suffixOverrides="," >
            spec_id,
            spec_type,
            input_type,
            product_stock_id,
            name,
            unit,
            package_unit,
            mark,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id,
            num
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{specId},
            #{specType},
            #{inputType},
            #{productStockId},
            #{name},
            #{unit},
            #{packageUnit},
            #{mark},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{regionDeptId},
            #{num}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_spec
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="specId != null" >
                spec_id,
            </if>
            <if test="specType != null" >
                spec_type,
            </if>
            <if test="inputType != null" >
                input_type,
            </if>
            <if test="productStockId != null" >
                product_stock_id,
            </if>
            <if test="name != null" >
                name,
            </if>
            <if test="unit != null" >
                unit,
            </if>
            <if test="packageUnit != null" >
                package_unit,
            </if>
            <if test="mark != null" >
                mark,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id,
            </if>
            <if test="num != null" >
                num
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="specId != null" >
                #{specId},
            </if>
            <if test="specType != null" >
                #{specType},
            </if>
            <if test="inputType != null" >
                #{inputType},
            </if>
            <if test="productStockId != null" >
                #{productStockId},
            </if>
            <if test="name != null" >
                #{name},
            </if>
            <if test="unit != null" >
                #{unit},
            </if>
            <if test="packageUnit != null" >
                #{packageUnit},
            </if>
            <if test="mark != null" >
                #{mark},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId},
            </if>
            <if test="num != null" >
                #{num}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_spec
        <trim prefix="(" suffix=")" suffixOverrides="," >
            spec_id,
            spec_type,
            input_type,
            product_stock_id,
            name,
            unit,
            package_unit,
            mark,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id,
            num
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.specId},
                #{item.specType},
                #{item.inputType},
                #{item.productStockId},
                #{item.name},
                #{item.unit},
                #{item.packageUnit},
                #{item.mark},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.regionDeptId},
                #{item.num}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_spec
          <set>
            <if test="specType != null" >
                spec_type=#{specType},
            </if>
            <if test="inputType != null" >
                input_type=#{inputType},
            </if>
            <if test="productStockId != null" >
                product_stock_id=#{productStockId},
            </if>
            <if test="name != null" >
                name=#{name},
            </if>
            <if test="unit != null" >
                unit=#{unit},
            </if>
            <if test="packageUnit != null" >
                package_unit=#{packageUnit},
            </if>
            <if test="mark != null" >
                mark=#{mark},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
            <if test="num != null" >
                num=#{num},
            </if>
          </set>
         where spec_id = #{specId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_spec
          <set>
                spec_type=#{specType},
                input_type=#{inputType},
                product_stock_id=#{productStockId},
                name=#{name},
                unit=#{unit},
                package_unit=#{packageUnit},
                mark=#{mark},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                region_dept_id=#{regionDeptId},
                num=#{num},
          </set>
         where spec_id = #{specId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
