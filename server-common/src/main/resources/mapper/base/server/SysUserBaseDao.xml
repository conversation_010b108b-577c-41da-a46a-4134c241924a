<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ===========通过 CodeGenerator 工具自动生成，请勿手工修改！！！====== -->
<!-- ============================================================== -->
<mapper namespace="com.jorchi.server.dao.SysUserDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.server.po.SysUser">
        <id column="user_id" property="userId" />
        <result column="dept_id" property="deptId" />
        <result column="user_name" property="userName" />
        <result column="nick_name" property="nickName" />
        <result column="user_type" property="userType" />
        <result column="email" property="email" />
        <result column="phonenumber" property="phoneNumber" />
        <result column="sex" property="sex" />
        <result column="avatar" property="avatar" />
        <result column="password" property="password" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="login_ip" property="loginIp" />
        <result column="login_date" property="loginDate" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="force_change_pwd" property="forceChangePwd" />
        <result column="salt" property="salt" />
        <result column="idcard" property="idcard" />
        <result column="pin_yin_1" property="pinYin1" />
        <result column="pin_yin_2" property="pinYin2" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        user_id,
        dept_id,
        user_name,
        nick_name,
        user_type,
        email,
        phonenumber,
        sex,
        avatar,
        password,
        status,
        del_flag,
        login_ip,
        login_date,
        create_by,
        create_time,
        update_by,
        update_time,
        remark,
        force_change_pwd,
        salt,
        idcard,
        pin_yin_1,
        pin_yin_2
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="deptId != null">
            and dept_id = #{deptId}
        </if>
        <if test="userName != null and userName != ''">
            and user_name = #{userName}
        </if>
        <if test="nickName != null and nickName != ''">
            and nick_name = #{nickName}
        </if>
        <if test="userType != null and userType != ''">
            and user_type = #{userType}
        </if>
        <if test="email != null and email != ''">
            and email = #{email}
        </if>
        <if test="phoneNumber != null and phoneNumber != ''">
            and phonenumber = #{phoneNumber}
        </if>
        <if test="sex != null and sex != ''">
            and sex = #{sex}
        </if>
        <if test="avatar != null and avatar != ''">
            and avatar = #{avatar}
        </if>
        <if test="password != null and password != ''">
            and password = #{password}
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="delFlag != null and delFlag != ''">
            and del_flag = #{delFlag}
        </if>
        <if test="loginIp != null and loginIp != ''">
            and login_ip = #{loginIp}
        </if>
        <if test="loginDate != null">
            and login_date = #{loginDate}
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="remark != null and remark != ''">
            and remark = #{remark}
        </if>
        <if test="forceChangePwd != null and forceChangePwd != ''">
            and force_change_pwd = #{forceChangePwd}
        </if>
        <if test="salt != null and salt != ''">
            and salt = #{salt}
        </if>
        <if test="idcard != null and idcard != ''">
            and idcard = #{idcard}
        </if>
        <if test="pinYin1 != null and pinYin1 != ''">
            and pin_yin_1 = #{pinYin1}
        </if>
        <if test="pinYin2 != null and pinYin2 != ''">
            and pin_yin_2 = #{pinYin2}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_sys_user
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sys_user
         where user_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_sys_user
         where user_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sys_user
         where user_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sys_user
         where user_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.userId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_sys_user
         where user_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_sys_user
         where user_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_sys_user
         where user_id = #{userId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_sys_user
         where user_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.userId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_sys_user
        <trim prefix="(" suffix=")" suffixOverrides="," >
            user_id,
            dept_id,
            user_name,
            nick_name,
            user_type,
            email,
            phonenumber,
            sex,
            avatar,
            password,
            status,
            del_flag,
            login_ip,
            login_date,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            force_change_pwd,
            salt,
            idcard,
            pin_yin_1,
            pin_yin_2
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{userId},
            #{deptId},
            #{userName},
            #{nickName},
            #{userType},
            #{email},
            #{phoneNumber},
            #{sex},
            #{avatar},
            #{password},
            #{status},
            #{delFlag},
            #{loginIp},
            #{loginDate},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{remark},
            #{forceChangePwd},
            #{salt},
            #{idcard},
            #{pinYin1},
            #{pinYin2}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_sys_user
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                user_id,
            </if>
            <if test="deptId != null" >
                dept_id,
            </if>
            <if test="userName != null" >
                user_name,
            </if>
            <if test="nickName != null" >
                nick_name,
            </if>
            <if test="userType != null" >
                user_type,
            </if>
            <if test="email != null" >
                email,
            </if>
            <if test="phoneNumber != null" >
                phonenumber,
            </if>
            <if test="sex != null" >
                sex,
            </if>
            <if test="avatar != null" >
                avatar,
            </if>
            <if test="password != null" >
                password,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="delFlag != null" >
                del_flag,
            </if>
            <if test="loginIp != null" >
                login_ip,
            </if>
            <if test="loginDate != null" >
                login_date,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="remark != null" >
                remark,
            </if>
            <if test="forceChangePwd != null" >
                force_change_pwd,
            </if>
            <if test="salt != null" >
                salt,
            </if>
            <if test="idcard != null" >
                idcard,
            </if>
            <if test="pinYin1 != null" >
                pin_yin_1,
            </if>
            <if test="pinYin2 != null" >
                pin_yin_2
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null" >
                #{userId},
            </if>
            <if test="deptId != null" >
                #{deptId},
            </if>
            <if test="userName != null" >
                #{userName},
            </if>
            <if test="nickName != null" >
                #{nickName},
            </if>
            <if test="userType != null" >
                #{userType},
            </if>
            <if test="email != null" >
                #{email},
            </if>
            <if test="phoneNumber != null" >
                #{phoneNumber},
            </if>
            <if test="sex != null" >
                #{sex},
            </if>
            <if test="avatar != null" >
                #{avatar},
            </if>
            <if test="password != null" >
                #{password},
            </if>
            <if test="status != null" >
                #{status},
            </if>
            <if test="delFlag != null" >
                #{delFlag},
            </if>
            <if test="loginIp != null" >
                #{loginIp},
            </if>
            <if test="loginDate != null" >
                #{loginDate},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="remark != null" >
                #{remark},
            </if>
            <if test="forceChangePwd != null" >
                #{forceChangePwd},
            </if>
            <if test="salt != null" >
                #{salt},
            </if>
            <if test="idcard != null" >
                #{idcard},
            </if>
            <if test="pinYin1 != null" >
                #{pinYin1},
            </if>
            <if test="pinYin2 != null" >
                #{pinYin2}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_sys_user
        <trim prefix="(" suffix=")" suffixOverrides="," >
            user_id,
            dept_id,
            user_name,
            nick_name,
            user_type,
            email,
            phonenumber,
            sex,
            avatar,
            password,
            status,
            del_flag,
            login_ip,
            login_date,
            create_by,
            create_time,
            update_by,
            update_time,
            remark,
            force_change_pwd,
            salt,
            idcard,
            pin_yin_1,
            pin_yin_2
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.userId},
                #{item.deptId},
                #{item.userName},
                #{item.nickName},
                #{item.userType},
                #{item.email},
                #{item.phoneNumber},
                #{item.sex},
                #{item.avatar},
                #{item.password},
                #{item.status},
                #{item.delFlag},
                #{item.loginIp},
                #{item.loginDate},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.remark},
                #{item.forceChangePwd},
                #{item.salt},
                #{item.idcard},
                #{item.pinYin1},
                #{item.pinYin2}
            </trim>
        </foreach>
    </insert>
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update sys_user
          <set>
            <if test="deptId != null" >
                dept_id=#{deptId},
            </if>
            <if test="userName != null" >
                user_name=#{userName},
            </if>
            <if test="nickName != null" >
                nick_name=#{nickName},
            </if>
            <if test="userType != null" >
                user_type=#{userType},
            </if>
            <if test="email != null" >
                email=#{email},
            </if>
            <if test="phoneNumber != null" >
                phonenumber=#{phoneNumber},
            </if>
            <if test="sex != null" >
                sex=#{sex},
            </if>
            <if test="avatar != null" >
                avatar=#{avatar},
            </if>
            <if test="password != null" >
                password=#{password},
            </if>
            <if test="status != null" >
                status=#{status},
            </if>
            <if test="delFlag != null" >
                del_flag=#{delFlag},
            </if>
            <if test="loginIp != null" >
                login_ip=#{loginIp},
            </if>
            <if test="loginDate != null" >
                login_date=#{loginDate},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="remark != null" >
                remark=#{remark},
            </if>
            <if test="forceChangePwd != null" >
                force_change_pwd=#{forceChangePwd},
            </if>
            <if test="salt != null" >
                salt=#{salt},
            </if>
            <if test="idcard != null" >
                idcard=#{idcard},
            </if>
            <if test="pinYin1 != null" >
                pin_yin_1=#{pinYin1},
            </if>
            <if test="pinYin2 != null" >
                pin_yin_2=#{pinYin2},
            </if>
          </set>
         where user_id = #{userId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <sql id="Base_Update_By_PrimaryKey">
        update sys_user
          <set>
                dept_id=#{deptId},
                user_name=#{userName},
                nick_name=#{nickName},
                user_type=#{userType},
                email=#{email},
                phonenumber=#{phoneNumber},
                sex=#{sex},
                avatar=#{avatar},
                password=#{password},
                status=#{status},
                del_flag=#{delFlag},
                login_ip=#{loginIp},
                login_date=#{loginDate},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                remark=#{remark},
                force_change_pwd=#{forceChangePwd},
                salt=#{salt},
                idcard=#{idcard},
                pin_yin_1=#{pinYin1},
                pin_yin_2=#{pinYin2}
          </set>
         where user_id = #{userId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
